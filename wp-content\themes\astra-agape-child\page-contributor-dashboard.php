<?php
/**
 * Contributor Dashboard Page Template for Agape SDA Church
 * Secure dashboard for content contributors
 */

// Security check - redirect if not logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

// Check if user has contributor permissions
$current_user = wp_get_current_user();
if (!in_array('agape_contributor', $current_user->roles) && !in_array('administrator', $current_user->roles)) {
    wp_die('You do not have permission to access this page. Please contact the church administrator.');
}

get_header(); ?>

<main id="main" class="site-main contributor-dashboard" role="main">
    
    <div class="dashboard-header">
        <div class="container">
            <h1>📝 Contributor Dashboard</h1>
            <p class="dashboard-subtitle">Welcome, <?php echo esc_html($current_user->display_name); ?>! Share resources with the Agape SDA Church community.</p>
            
            <div class="user-info">
                <div class="user-details">
                    <strong>👤 User:</strong> <?php echo esc_html($current_user->display_name); ?><br>
                    <strong>✉️ Email:</strong> <?php echo esc_html($current_user->user_email); ?><br>
                    <strong>🔐 Role:</strong> <?php echo esc_html(implode(', ', $current_user->roles)); ?>
                </div>
                <div class="dashboard-actions">
                    <a href="<?php echo wp_logout_url(home_url()); ?>" class="btn-secondary">🚪 Logout</a>
                    <a href="<?php echo admin_url('profile.php'); ?>" class="btn-tertiary">⚙️ Edit Profile</a>
                </div>
            </div>
        </div>
    </div>

    <div class="dashboard-content">
        <div class="container">
            
            <!-- Quick Stats -->
            <div class="dashboard-stats">
                <h2>📊 Your Contributions</h2>
                <div class="stats-grid">
                    <?php
                    $user_id = get_current_user_id();
                    $submission_types = array(
                        'blog_submission' => array('name' => 'Blog Posts', 'icon' => '📝'),
                        'file_submission' => array('name' => 'Files', 'icon' => '📁'),
                        'video_submission' => array('name' => 'Videos', 'icon' => '📺'),
                        'book_submission' => array('name' => 'Books', 'icon' => '📚')
                    );
                    
                    foreach ($submission_types as $type => $info) {
                        $pending = get_posts(array(
                            'post_type' => $type,
                            'post_status' => 'draft',
                            'author' => $user_id,
                            'numberposts' => -1
                        ));
                        
                        $approved = get_posts(array(
                            'post_type' => $type,
                            'post_status' => 'publish',
                            'author' => $user_id,
                            'numberposts' => -1
                        ));
                        ?>
                        <div class="stat-card">
                            <div class="stat-icon"><?php echo $info['icon']; ?></div>
                            <div class="stat-info">
                                <h3><?php echo $info['name']; ?></h3>
                                <p><strong><?php echo count($pending); ?></strong> pending</p>
                                <p><strong><?php echo count($approved); ?></strong> approved</p>
                            </div>
                        </div>
                        <?php
                    }
                    ?>
                </div>
            </div>

            <!-- Submission Forms -->
            <div class="submission-forms">
                <h2>➕ Submit New Content</h2>
                <div class="forms-tabs">
                    <button class="tab-btn active" data-tab="blog">📝 Blog Post</button>
                    <button class="tab-btn" data-tab="file">📁 File Resource</button>
                    <button class="tab-btn" data-tab="video">📺 Video Resource</button>
                    <button class="tab-btn" data-tab="book">📚 Book Resource</button>
                </div>

                <!-- Blog Submission Form -->
                <div class="submission-form active" id="blog-form">
                    <h3>📝 Submit Blog Post</h3>
                    <form class="content-submission-form" data-type="blog">
                        <?php wp_nonce_field('agape_submit_content', 'agape_submission_nonce'); ?>
                        
                        <div class="form-group">
                            <label for="blog-title">Title *</label>
                            <input type="text" id="blog-title" name="title" required placeholder="Enter blog post title">
                        </div>
                        
                        <div class="form-group">
                            <label for="blog-category">Category *</label>
                            <select id="blog-category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="sermons">Sermons</option>
                                <option value="community">Community</option>
                                <option value="health">Health Ministry</option>
                                <option value="youth">Youth</option>
                                <option value="devotional">Devotional</option>
                                <option value="events">Events</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="blog-excerpt">Excerpt</label>
                            <textarea id="blog-excerpt" name="excerpt" rows="3" placeholder="Brief description of your blog post"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="blog-content">Content *</label>
                            <textarea id="blog-content" name="content" rows="10" required placeholder="Write your blog post content here..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="blog-tags">Tags</label>
                            <input type="text" id="blog-tags" name="tags" placeholder="Enter tags separated by commas">
                        </div>
                        
                        <div class="form-group">
                            <label for="blog-image">Featured Image</label>
                            <input type="file" id="blog-image" name="submission_file" accept="image/*">
                            <small>Upload a featured image for your blog post (optional)</small>
                        </div>
                        
                        <button type="submit" class="btn-primary">📤 Submit Blog Post</button>
                    </form>
                </div>

                <!-- File Submission Form -->
                <div class="submission-form" id="file-form">
                    <h3>📁 Submit File Resource</h3>
                    <form class="content-submission-form" data-type="file">
                        <?php wp_nonce_field('agape_submit_content', 'agape_submission_nonce'); ?>
                        
                        <div class="form-group">
                            <label for="file-title">Title *</label>
                            <input type="text" id="file-title" name="title" required placeholder="Enter file title">
                        </div>
                        
                        <div class="form-group">
                            <label for="file-type">File Type *</label>
                            <select id="file-type" name="file_type" required>
                                <option value="">Select File Type</option>
                                <option value="sermon">Sermon Audio</option>
                                <option value="presentation">Presentation</option>
                                <option value="notes">Study Notes</option>
                                <option value="pdf">PDF Document</option>
                                <option value="audio">Audio Recording</option>
                                <option value="video">Video File</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="file-category">Category *</label>
                            <select id="file-category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="sermons">Sermons</option>
                                <option value="presentations">Presentations</option>
                                <option value="study-notes">Study Notes</option>
                                <option value="health">Health Ministry</option>
                                <option value="youth">Youth Programs</option>
                                <option value="music">Music & Worship</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="file-description">Description *</label>
                            <textarea id="file-description" name="content" rows="5" required placeholder="Describe what this file contains and how it can be used..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="file-duration">Duration (if audio/video)</label>
                            <input type="text" id="file-duration" name="file_duration" placeholder="e.g., 45 minutes">
                        </div>
                        
                        <div class="form-group">
                            <label for="file-upload">File *</label>
                            <input type="file" id="file-upload" name="submission_file" required>
                            <small>Upload your file (PDF, audio, video, presentation, etc.)</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="file-featured-image">Featured Image</label>
                            <input type="file" id="file-featured-image" name="featured_image" accept="image/*">
                            <small>Upload a thumbnail or featured image (optional)</small>
                        </div>
                        
                        <button type="submit" class="btn-primary">📤 Submit File Resource</button>
                    </form>
                </div>

                <!-- Video Submission Form -->
                <div class="submission-form" id="video-form">
                    <h3>📺 Submit Video Resource</h3>
                    <form class="content-submission-form" data-type="video">
                        <?php wp_nonce_field('agape_submit_content', 'agape_submission_nonce'); ?>
                        
                        <div class="form-group">
                            <label for="video-title">Title *</label>
                            <input type="text" id="video-title" name="title" required placeholder="Enter video title">
                        </div>
                        
                        <div class="form-group">
                            <label for="video-category">Category *</label>
                            <select id="video-category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="sermons">Sermons</option>
                                <option value="documentary">Documentaries</option>
                                <option value="prophecy">Prophecy</option>
                                <option value="health">Health</option>
                                <option value="youth">Youth</option>
                                <option value="evangelism">Evangelism</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="video-speaker">Speaker/Author</label>
                            <input type="text" id="video-speaker" name="speaker" placeholder="Enter speaker or author name">
                        </div>
                        
                        <div class="form-group">
                            <label for="video-url">Video URL</label>
                            <input type="url" id="video-url" name="video_url" placeholder="https://youtube.com/watch?v=... or upload file below">
                        </div>
                        
                        <div class="form-group">
                            <label for="video-file">Or Upload Video File</label>
                            <input type="file" id="video-file" name="submission_file" accept="video/*">
                            <small>Upload video file if not using URL</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="video-duration">Duration</label>
                            <input type="text" id="video-duration" name="video_duration" placeholder="e.g., 1h 30min">
                        </div>
                        
                        <div class="form-group">
                            <label for="video-description">Description *</label>
                            <textarea id="video-description" name="content" rows="5" required placeholder="Describe the video content, topics covered, and target audience..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="video-related-links">Related Resources</label>
                            <textarea id="video-related-links" name="related_links" rows="4" placeholder="Add related links, study guides, or additional resources (one per line)"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="video-thumbnail">Video Thumbnail</label>
                            <input type="file" id="video-thumbnail" name="featured_image" accept="image/*">
                            <small>Upload a thumbnail image for the video</small>
                        </div>
                        
                        <button type="submit" class="btn-primary">📤 Submit Video Resource</button>
                    </form>
                </div>

                <!-- Book Submission Form -->
                <div class="submission-form" id="book-form">
                    <h3>📚 Submit Book Resource</h3>
                    <form class="content-submission-form" data-type="book">
                        <?php wp_nonce_field('agape_submit_content', 'agape_submission_nonce'); ?>
                        
                        <div class="form-group">
                            <label for="book-title">Book Title *</label>
                            <input type="text" id="book-title" name="title" required placeholder="Enter book title">
                        </div>
                        
                        <div class="form-group">
                            <label for="book-author">Author *</label>
                            <input type="text" id="book-author" name="author" required placeholder="Enter author name">
                        </div>
                        
                        <div class="form-group">
                            <label for="book-category">Category *</label>
                            <select id="book-category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="doctrine">Doctrine</option>
                                <option value="prophecy">Prophecy</option>
                                <option value="health">Health</option>
                                <option value="devotional">Devotional</option>
                                <option value="youth">Youth</option>
                                <option value="children">Children</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="book-description">Description *</label>
                            <textarea id="book-description" name="content" rows="5" required placeholder="Describe the book content, themes, and target audience..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="book-pages">Number of Pages</label>
                            <input type="number" id="book-pages" name="pages" placeholder="e.g., 256">
                        </div>
                        
                        <div class="form-group">
                            <label for="book-isbn">ISBN (if available)</label>
                            <input type="text" id="book-isbn" name="isbn" placeholder="Enter ISBN number">
                        </div>
                        
                        <div class="form-group">
                            <label for="book-file">Book File (PDF) *</label>
                            <input type="file" id="book-file" name="submission_file" accept=".pdf" required>
                            <small>Upload the book in PDF format</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="book-cover">Book Cover Image</label>
                            <input type="file" id="book-cover" name="featured_image" accept="image/*">
                            <small>Upload book cover image</small>
                        </div>
                        
                        <button type="submit" class="btn-primary">📤 Submit Book Resource</button>
                    </form>
                </div>
            </div>

            <!-- Recent Submissions -->
            <div class="recent-submissions">
                <h2>📋 Your Recent Submissions</h2>
                <div class="submissions-list">
                    <?php
                    $recent_submissions = array();
                    foreach ($submission_types as $type => $info) {
                        $posts = get_posts(array(
                            'post_type' => $type,
                            'author' => $user_id,
                            'numberposts' => 5,
                            'post_status' => array('draft', 'publish')
                        ));
                        foreach ($posts as $post) {
                            $post->submission_type = $info['name'];
                            $post->submission_icon = $info['icon'];
                            $recent_submissions[] = $post;
                        }
                    }
                    
                    // Sort by date
                    usort($recent_submissions, function($a, $b) {
                        return strtotime($b->post_date) - strtotime($a->post_date);
                    });
                    
                    if (!empty($recent_submissions)) {
                        foreach (array_slice($recent_submissions, 0, 10) as $submission) {
                            $status = get_post_meta($submission->ID, '_submission_status', true);
                            $status_class = $submission->post_status === 'publish' ? 'approved' : 'pending';
                            $status_text = $submission->post_status === 'publish' ? 'Approved' : 'Pending Review';
                            ?>
                            <div class="submission-item <?php echo $status_class; ?>">
                                <div class="submission-icon"><?php echo $submission->submission_icon; ?></div>
                                <div class="submission-details">
                                    <h4><?php echo esc_html($submission->post_title); ?></h4>
                                    <p class="submission-meta">
                                        <span class="submission-type"><?php echo $submission->submission_type; ?></span> • 
                                        <span class="submission-date"><?php echo date('M j, Y', strtotime($submission->post_date)); ?></span> • 
                                        <span class="submission-status status-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </p>
                                </div>
                            </div>
                            <?php
                        }
                    } else {
                        echo '<p class="no-submissions">No submissions yet. Use the forms above to submit your first resource!</p>';
                    }
                    ?>
                </div>
            </div>

        </div>
    </div>

</main>

<script>
// Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching
    const tabBtns = document.querySelectorAll('.tab-btn');
    const forms = document.querySelectorAll('.submission-form');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const tabType = this.dataset.tab;
            
            // Update active tab
            tabBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Show corresponding form
            forms.forEach(form => {
                form.classList.remove('active');
                if (form.id === tabType + '-form') {
                    form.classList.add('active');
                }
            });
        });
    });
    
    // Form submission
    const submissionForms = document.querySelectorAll('.content-submission-form');
    submissionForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'agape_submit_content');
            formData.append('submission_type', this.dataset.type);
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '⏳ Submitting...';
            submitBtn.disabled = true;
            
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.data.message);
                    this.reset();
                    location.reload(); // Refresh to show new submission
                } else {
                    alert('❌ Error: ' + data.data);
                }
            })
            .catch(error => {
                alert('❌ An error occurred. Please try again.');
                console.error('Error:', error);
            })
            .finally(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    });
});
</script>

<?php get_footer(); ?>
