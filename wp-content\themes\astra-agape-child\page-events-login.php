<?php
/**
 * Events Login Page Template for Agape SDA Church
 * Secure login for church clerks and elders to manage events
 */

// Redirect if already logged in with proper permissions
if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    $allowed_roles = array('administrator', 'church_clerk', 'church_elder');
    
    if (array_intersect($allowed_roles, $current_user->roles)) {
        wp_redirect(home_url('/events-management'));
        exit;
    }
}

get_header(); ?>

<main id="main" class="site-main events-login" role="main">
    
    <div class="login-container">
        <div class="container">
            
            <div class="login-card">
                <div class="login-header">
                    <h1>🔐 Events Management Login</h1>
                    <p>Access the church events management system</p>
                    <div class="access-info">
                        <small>👥 <strong>Authorized Personnel Only:</strong> Church Clerks & Elders</small>
                    </div>
                </div>

                <div class="login-form-container">
                    <?php
                    // Display any error messages
                    if (isset($_GET['login']) && $_GET['login'] === 'failed') {
                        echo '<div class="login-error">❌ Invalid username or password. Please try again.</div>';
                    }
                    
                    if (isset($_GET['login']) && $_GET['login'] === 'empty') {
                        echo '<div class="login-error">❌ Please enter both username and password.</div>';
                    }
                    
                    if (isset($_GET['access']) && $_GET['access'] === 'denied') {
                        echo '<div class="login-error">❌ You do not have permission to access the events management system. Only church clerks and elders can manage events.</div>';
                    }
                    ?>

                    <form name="loginform" id="loginform" action="<?php echo esc_url(site_url('wp-login.php', 'login_post')); ?>" method="post">
                        <div class="form-group">
                            <label for="user_login">👤 Username or Email</label>
                            <input type="text" name="log" id="user_login" class="input" value="" size="20" autocapitalize="off" required />
                        </div>
                        
                        <div class="form-group">
                            <label for="user_pass">🔒 Password</label>
                            <input type="password" name="pwd" id="user_pass" class="input" value="" size="20" required />
                        </div>
                        
                        <div class="form-group checkbox-group">
                            <label for="rememberme">
                                <input name="rememberme" type="checkbox" id="rememberme" value="forever" />
                                Remember Me
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <input type="submit" name="wp-submit" id="wp-submit" class="btn-primary" value="🚪 Access Events Management" />
                            <input type="hidden" name="redirect_to" value="<?php echo esc_attr(home_url('/events-management')); ?>" />
                        </div>
                    </form>

                    <div class="login-links">
                        <a href="<?php echo wp_lostpassword_url(); ?>">🔑 Forgot Password?</a>
                        <span class="separator">|</span>
                        <a href="#request-access" class="show-request">📝 Request Access</a>
                    </div>
                </div>

                <!-- Access Request Information -->
                <div class="request-access-container" id="request-access" style="display: none;">
                    <h2>📝 Request Events Management Access</h2>
                    <p>If you are a church clerk or elder and need access to the events management system, please contact the church administration.</p>
                    
                    <div class="access-requirements">
                        <h3>📋 Access Requirements</h3>
                        <ul>
                            <li><strong>Church Clerks:</strong> Officially appointed church clerks</li>
                            <li><strong>Church Elders:</strong> Ordained or appointed church elders</li>
                            <li><strong>Authorization:</strong> Must be approved by church board</li>
                            <li><strong>Training:</strong> Basic computer and website training recommended</li>
                        </ul>
                    </div>
                    
                    <div class="contact-admin">
                        <h3>📞 Contact Church Administration</h3>
                        <p>To request access, please contact:</p>
                        <div class="contact-options">
                            <a href="tel:+27685180531" class="contact-link">📞 +27 68 518 0531</a>
                            <a href="mailto:<EMAIL>" class="contact-link">✉️ <EMAIL></a>
                            <?php echo do_shortcode('[whatsapp_contact text="💬 WhatsApp"]'); ?>
                        </div>
                        <p class="note">
                            <em>Please include your full name, position in the church, and reason for requesting access.</em>
                        </p>
                    </div>
                    
                    <button type="button" class="btn-secondary show-login">🔙 Back to Login</button>
                </div>

                <div class="system-info">
                    <h3>ℹ️ About Events Management System</h3>
                    <div class="info-content">
                        <p><strong>What can you do with this system?</strong></p>
                        <ul>
                            <li>📅 Create and schedule church events</li>
                            <li>✏️ Edit existing event details</li>
                            <li>🗑️ Remove outdated or cancelled events</li>
                            <li>📋 Manage event categories and departments</li>
                            <li>📞 Set contact information for events</li>
                            <li>🎯 Set event priorities and visibility</li>
                            <li>📊 View events statistics and overview</li>
                        </ul>
                        
                        <p><strong>Event Information You Can Manage:</strong></p>
                        <ul>
                            <li>Event name, date, and time</li>
                            <li>Hosting department</li>
                            <li>Location and contact details</li>
                            <li>Registration requirements</li>
                            <li>Event descriptions and images</li>
                            <li>Cost and attendance limits</li>
                        </ul>
                        
                        <p><strong>Security Features:</strong></p>
                        <ul>
                            <li>🔐 Secure login required</li>
                            <li>👥 Role-based access control</li>
                            <li>📝 Activity logging and tracking</li>
                            <li>🔒 Data protection and backup</li>
                        </ul>
                    </div>
                </div>

                <div class="quick-links">
                    <h3>🔗 Quick Links</h3>
                    <div class="links-grid">
                        <a href="<?php echo home_url('/events'); ?>" class="quick-link">
                            👁️ View Public Events Page
                        </a>
                        <a href="<?php echo home_url('/'); ?>" class="quick-link">
                            🏠 Church Homepage
                        </a>
                        <a href="<?php echo home_url('/contact'); ?>" class="quick-link">
                            📞 Contact Church
                        </a>
                        <a href="<?php echo home_url('/about'); ?>" class="quick-link">
                            ℹ️ About Our Church
                        </a>
                    </div>
                </div>

            </div>
            
        </div>
    </div>

</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const showRequestBtn = document.querySelector('.show-request');
    const showLoginBtn = document.querySelector('.show-login');
    const loginForm = document.querySelector('.login-form-container');
    const requestAccess = document.querySelector('.request-access-container');
    
    // Toggle between login and request access
    showRequestBtn.addEventListener('click', function(e) {
        e.preventDefault();
        loginForm.style.display = 'none';
        requestAccess.style.display = 'block';
    });
    
    showLoginBtn.addEventListener('click', function(e) {
        e.preventDefault();
        requestAccess.style.display = 'none';
        loginForm.style.display = 'block';
    });
});
</script>

<style>
/* Events Login Page Specific Styles */
.events-login {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    min-height: 100vh;
    padding: 50px 0;
}

.login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
}

.login-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    padding: 40px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    color: #1976d2;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.login-header p {
    color: #666;
    margin-bottom: 15px;
}

.access-info {
    background: #e3f2fd;
    padding: 10px 15px;
    border-radius: 8px;
    border-left: 4px solid #1976d2;
}

.access-info small {
    color: #1976d2;
    font-weight: 600;
}

.login-error {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #1976d2;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    width: 100%;
}

.btn-primary {
    background: #1976d2;
    color: white;
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
    border: 2px solid #e0e0e0;
    margin-top: 20px;
}

.btn-primary:hover {
    background: #1565c0;
    transform: translateY(-2px);
}

.btn-secondary:hover {
    background: #e0e0e0;
}

.login-links {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.login-links a {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
}

.login-links .separator {
    margin: 0 10px;
    color: #ccc;
}

.system-info,
.quick-links {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.system-info h3,
.quick-links h3 {
    color: #1976d2;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.info-content ul {
    margin: 10px 0;
    padding-left: 20px;
}

.info-content li {
    margin-bottom: 5px;
    color: #666;
}

.access-requirements {
    background: #fff3cd;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
    border-left: 4px solid #ffc107;
}

.access-requirements h3 {
    color: #856404;
    margin-bottom: 10px;
}

.access-requirements ul {
    margin: 0;
    padding-left: 20px;
}

.access-requirements li {
    color: #856404;
    margin-bottom: 5px;
}

.contact-admin {
    background: #d1ecf1;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
    border-left: 4px solid #17a2b8;
}

.contact-admin h3 {
    color: #0c5460;
    margin-bottom: 10px;
}

.contact-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin: 15px 0;
}

.contact-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
    padding: 8px 12px;
    background: white;
    border-radius: 20px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.contact-link:hover {
    background: #e3f2fd;
    border-color: #1976d2;
}

.note {
    font-size: 0.9rem;
    color: #0c5460;
    font-style: italic;
    margin-top: 10px;
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.quick-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
    padding: 10px 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
    text-align: center;
}

.quick-link:hover {
    background: #e3f2fd;
    border-color: #1976d2;
}

@media (max-width: 768px) {
    .login-card {
        margin: 20px;
        padding: 30px 20px;
        max-height: 95vh;
    }
    
    .contact-options {
        flex-direction: column;
    }
    
    .contact-link {
        text-align: center;
    }
    
    .links-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>
