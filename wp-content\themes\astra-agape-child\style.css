/*
Theme Name: Astra Agape Child
Description: Child theme of Astra optimized for Agape SDA Church Vanderbijlpark with enhanced SEO features
Author: Agape SDA Church
Template: astra
Version: 1.0.0
Text Domain: astra-agape-child
*/

/* Import parent theme styles */
@import url("../astra/style.css");

/* Enhanced styles for Agape SDA Church */

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-content h1 small {
    font-size: 1.2rem;
    font-weight: 400;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 30px;
    line-height: 1.6;
}

.cta-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin: 30px 0;
}

.btn-primary, .btn-secondary, .btn-tertiary {
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: #fff;
    color: #1976d2;
}

.btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.btn-tertiary {
    background: rgba(255,255,255,0.2);
    color: #fff;
}

.btn-primary:hover {
    background: #f5f5f5;
    transform: translateY(-2px);
}

.btn-secondary:hover, .btn-tertiary:hover {
    background: rgba(255,255,255,0.1);
    transform: translateY(-2px);
}

.quick-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 40px;
    font-size: 0.95rem;
}

.info-item {
    background: rgba(255,255,255,0.1);
    padding: 10px 15px;
    border-radius: 20px;
}

/* Live Service Banner */
.live-service-banner {
    background: #f8f9fa;
    padding: 40px 0;
}

.service-schedule {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin: 20px 0;
}

.schedule-item {
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    min-width: 200px;
}

.welcome-message {
    text-align: center;
    font-style: italic;
    color: #666;
    margin-top: 20px;
}

/* Benefits Grid */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.benefit-item {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-5px);
}

.benefit-item h3 {
    color: #1976d2;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

/* Contact Grid */
.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.contact-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-3px);
}

.contact-card h3 {
    color: #1976d2;
    margin-bottom: 15px;
}

.btn-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    margin-top: 10px;
}

.btn-link:hover {
    text-decoration: underline;
}

.sda-highlight {
    color: #1976d2;
    font-weight: 600;
}

/* Beliefs Section */
.beliefs-comparison {
    margin: 30px 0;
}

.belief-highlight {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 30px;
}

.beliefs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.belief-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #1976d2;
}

.belief-card h4 {
    color: #1976d2;
    margin-bottom: 10px;
}

/* Ministry Cards */
.ministry-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.ministry-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.ministry-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.ministry-card.featured {
    border: 2px solid #1976d2;
    background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
}

.ministry-card h3 {
    color: #1976d2;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.card-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    margin-top: 15px;
    padding: 8px 16px;
    border: 1px solid #1976d2;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.card-link:hover {
    background: #1976d2;
    color: white;
}

.ministry-cta {
    text-align: center;
    margin-top: 40px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 15px;
}

/* Testimonials */
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.testimonial-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative;
}

.testimonial-card::before {
    content: '"';
    font-size: 4rem;
    color: #1976d2;
    position: absolute;
    top: -10px;
    left: 20px;
    opacity: 0.3;
}

.stars {
    color: #ffc107;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.testimonial-card cite {
    font-style: italic;
    color: #666;
    font-weight: 600;
}

/* Community Stats */
.community-stats {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 15px;
    margin: 40px 0;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

.stat-item {
    text-align: center;
}

.stat-item strong {
    display: block;
    font-size: 2.5rem;
    color: #1976d2;
    font-weight: 700;
}

.stat-item span {
    color: #666;
    font-size: 0.9rem;
}

/* Events Section */
.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.event-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    transition: transform 0.3s ease;
}

.event-card:hover {
    transform: translateY(-3px);
}

.event-card.featured {
    border: 2px solid #1976d2;
}

.event-date {
    background: #1976d2;
    color: white;
    padding: 20px;
    text-align: center;
    min-width: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.event-date .day {
    font-size: 1.8rem;
    font-weight: 700;
}

.event-date .month {
    font-size: 0.9rem;
    text-transform: uppercase;
}

.event-info {
    padding: 20px;
    flex: 1;
}

.event-info h3 {
    color: #1976d2;
    margin-bottom: 10px;
}

.event-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    margin-top: 10px;
}

.events-cta {
    text-align: center;
    margin-top: 30px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .quick-info {
        flex-direction: column;
        gap: 15px;
    }

    .service-schedule {
        flex-direction: column;
        gap: 15px;
    }

    .schedule-item {
        min-width: auto;
    }

    .benefits-grid,
    .contact-grid,
    .beliefs-grid,
    .ministry-grid,
    .testimonials-grid,
    .stats-grid,
    .events-grid {
        grid-template-columns: 1fr;
    }

    .event-card {
        flex-direction: column;
    }

    .event-date {
        min-width: auto;
        flex-direction: row;
        justify-content: center;
        gap: 10px;
    }

    /* Mobile styles for new pages */
    .books-page .search-container input {
        width: 250px;
    }

    .books-grid,
    .blog-grid,
    .files-grid,
    .resources-grid {
        grid-template-columns: 1fr;
    }

    .featured-grid {
        grid-template-columns: 1fr;
    }

    .filter-tags,
    .blog-categories {
        justify-content: center;
    }

    .filter-btn,
    .category-btn {
        font-size: 0.85rem;
        padding: 6px 12px;
    }

    .book-actions,
    .file-actions,
    .resource-actions {
        flex-direction: column;
    }

    .book-actions .btn-primary,
    .book-actions .btn-secondary,
    .file-actions .btn-primary,
    .file-actions .btn-secondary,
    .resource-actions .btn-primary,
    .resource-actions .btn-secondary {
        min-width: auto;
        width: 100%;
    }

    .contact-options {
        flex-direction: column;
        gap: 15px;
    }

    .contact-options .btn-primary,
    .contact-options .btn-secondary {
        width: 100%;
        text-align: center;
    }

    .related-links {
        gap: 8px;
    }

    .related-link {
        font-size: 0.85rem;
        padding: 6px 10px;
    }

    .resources-grid {
        grid-template-columns: 1fr;
    }

    .video-thumbnail {
        height: 200px;
    }

    .resource-description {
        font-size: 0.9rem;
    }

    .guides-grid {
        grid-template-columns: 1fr;
    }

    .age-groups-grid {
        grid-template-columns: 1fr;
    }

    .schedule-grid {
        grid-template-columns: 1fr;
    }

    .location-grid {
        grid-template-columns: 1fr;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Church-specific styling */
.sabbath-emphasis {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin: 20px 0;
}

.ministry-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.ministry-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

/* Location Section */
.location-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

.location-info, .directions-info {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.location-info h3, .directions-info h3 {
    color: #1976d2;
    margin-bottom: 15px;
}

.location-info ul {
    list-style: none;
    padding: 0;
}

.location-info li {
    padding: 5px 0;
    color: #333;
}

/* Final CTA Section */
.final-cta-section {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.2rem;
    margin-bottom: 15px;
}

.cta-subtitle {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin: 40px 0;
}

.cta-option {
    background: rgba(255,255,255,0.1);
    padding: 30px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.cta-option.primary {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
}

.cta-option h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.cta-option p {
    margin-bottom: 20px;
    opacity: 0.9;
}

.final-message {
    margin-top: 50px;
    padding-top: 30px;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.final-message h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    font-style: italic;
}

.social-proof {
    margin-top: 20px;
    opacity: 0.8;
}

/* Books Page Styles */
.books-page .page-header {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.books-search {
    margin-top: 30px;
}

.search-container {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

.search-container input {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    width: 300px;
    font-size: 1rem;
}

.search-container button {
    padding: 12px 20px;
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 25px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-container button:hover {
    background: rgba(255,255,255,0.3);
}

.filter-tags {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.4);
}

.books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.featured-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.book-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.book-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.book-card.featured {
    border: 2px solid #1976d2;
    background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
}

.book-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.book-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.book-card:hover .book-image img {
    transform: scale(1.05);
}

.book-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(25, 118, 210, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.book-card:hover .book-overlay {
    opacity: 1;
}

.btn-preview,
.btn-download {
    padding: 10px 15px;
    background: white;
    color: #1976d2;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-preview:hover,
.btn-download:hover {
    background: #f5f5f5;
    transform: translateY(-2px);
}

.book-info {
    padding: 25px;
}

.book-info h3 {
    color: #1976d2;
    margin-bottom: 10px;
    font-size: 1.3rem;
    line-height: 1.3;
}

.book-author {
    color: #666;
    font-style: italic;
    margin-bottom: 15px;
    font-weight: 600;
}

.book-description {
    color: #333;
    line-height: 1.6;
    margin-bottom: 15px;
}

.book-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.book-category {
    background: #1976d2;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.book-pages {
    color: #666;
    font-size: 0.9rem;
}

.book-rating {
    color: #ffc107;
}

.book-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.book-actions .btn-primary,
.book-actions .btn-secondary {
    padding: 10px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    flex: 1;
    min-width: 120px;
}

.book-actions .btn-primary {
    background: #1976d2;
    color: white;
}

.book-actions .btn-secondary {
    background: transparent;
    color: #1976d2;
    border: 2px solid #1976d2;
}

.book-actions .btn-primary:hover {
    background: #1565c0;
    transform: translateY(-2px);
}

.book-actions .btn-secondary:hover {
    background: #1976d2;
    color: white;
    transform: translateY(-2px);
}

/* Study Guides Section */
.study-guides {
    background: #f8f9fa;
    padding: 50px 0;
    margin: 50px 0;
    border-radius: 20px;
}

.guides-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.guide-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.guide-card:hover {
    transform: translateY(-5px);
}

.guide-card h3 {
    color: #1976d2;
    margin-bottom: 15px;
}

.guide-card p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

/* Request Books Section */
.request-books {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    color: white;
    padding: 50px;
    border-radius: 20px;
    text-align: center;
    margin: 50px 0;
}

.request-books h2 {
    margin-bottom: 20px;
}

.request-info p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.contact-options {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.contact-options .btn-primary,
.contact-options .btn-secondary {
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.contact-options .btn-primary {
    background: white;
    color: #1976d2;
}

.contact-options .btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.contact-options .btn-primary:hover,
.contact-options .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Blog Page Styles */
.blog-page .page-header {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.blog-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.blog-featured-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.blog-featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-featured-image img {
    transform: scale(1.05);
}

.blog-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: #1976d2;
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.blog-content {
    padding: 25px;
}

.blog-title {
    color: #1976d2;
    margin-bottom: 10px;
    font-size: 1.3rem;
    line-height: 1.3;
}

.blog-meta {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 15px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.blog-excerpt {
    color: #333;
    line-height: 1.6;
    margin-bottom: 20px;
}

.blog-read-more {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.blog-read-more:hover {
    color: #1565c0;
    transform: translateX(5px);
}

/* Files Page Styles */
.files-page .page-header {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.files-search {
    margin-top: 30px;
}

.files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.file-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.file-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.file-card.featured {
    border: 2px solid #1976d2;
    background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
}

.file-icon {
    position: relative;
    height: 200px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
}

.file-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-icon-placeholder {
    font-size: 4rem;
    color: #1976d2;
    opacity: 0.7;
}

.file-type-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #1976d2;
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.file-info {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.file-info h3 {
    color: #1976d2;
    margin-bottom: 10px;
    font-size: 1.3rem;
    line-height: 1.3;
}

.file-meta {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.file-description {
    color: #333;
    line-height: 1.6;
    margin-bottom: 15px;
    flex: 1;
}

.file-details {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: #666;
}

.file-size {
    font-weight: 600;
    color: #1976d2;
}

.file-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: auto;
}

.file-actions .btn-primary,
.file-actions .btn-secondary {
    padding: 10px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    flex: 1;
    min-width: 140px;
}

.file-actions .btn-primary {
    background: #1976d2;
    color: white;
}

.file-actions .btn-secondary {
    background: transparent;
    color: #1976d2;
    border: 2px solid #1976d2;
}

.file-actions .btn-primary:hover {
    background: #1565c0;
    transform: translateY(-2px);
}

.file-actions .btn-secondary:hover {
    background: #1976d2;
    color: white;
    transform: translateY(-2px);
}

/* Upload Request Section */
.upload-request {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    color: white;
    padding: 50px;
    border-radius: 20px;
    text-align: center;
    margin: 50px 0;
}

.upload-request h2 {
    margin-bottom: 20px;
}

.upload-request .request-info p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.upload-request .note {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 20px;
}

/* Resources Page Styles */
.resources-page .page-header {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.resources-search {
    margin-top: 30px;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.resource-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.resource-card.featured {
    border: 2px solid #1976d2;
    background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
}

.video-thumbnail {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.resource-card:hover .video-thumbnail img {
    transform: scale(1.05);
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.resource-card:hover .play-overlay {
    opacity: 1;
}

.play-btn {
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-btn:hover {
    background: white;
    transform: scale(1.1);
}

.video-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 600;
}

.resource-info {
    padding: 25px;
}

.resource-info h3 {
    color: #1976d2;
    margin-bottom: 10px;
    font-size: 1.3rem;
    line-height: 1.3;
}

.resource-meta {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.resource-description-toggle {
    margin-bottom: 15px;
}

.description-toggle {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 0.9rem;
    color: #1976d2;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.description-toggle:hover {
    background: #e3f2fd;
    border-color: #1976d2;
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.resource-description {
    margin-bottom: 20px;
    line-height: 1.6;
    color: #333;
    transition: all 0.3s ease;
}

.resource-description.collapsed {
    max-height: 0;
    overflow: hidden;
    margin-bottom: 0;
    opacity: 0;
}

.resource-description.expanded {
    max-height: 1000px;
    opacity: 1;
}

.resource-description ul {
    margin: 15px 0;
    padding-left: 20px;
}

.resource-description li {
    margin-bottom: 5px;
}

.related-resources {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.related-resources h4 {
    color: #1976d2;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.related-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.related-link {
    color: #1976d2;
    text-decoration: none;
    padding: 8px 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.related-link:hover {
    background: #e3f2fd;
    border-color: #1976d2;
    transform: translateX(5px);
}

.resource-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.resource-actions .btn-primary,
.resource-actions .btn-secondary {
    padding: 12px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    flex: 1;
    min-width: 140px;
}

.resource-actions .btn-primary {
    background: #1976d2;
    color: white;
}

.resource-actions .btn-secondary {
    background: transparent;
    color: #1976d2;
    border: 2px solid #1976d2;
}

.resource-actions .btn-primary:hover {
    background: #1565c0;
    transform: translateY(-2px);
}

.resource-actions .btn-secondary:hover {
    background: #1976d2;
    color: white;
    transform: translateY(-2px);
}

/* Request Resources Section */
.request-resources {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    color: white;
    padding: 50px;
    border-radius: 20px;
    text-align: center;
    margin: 50px 0;
}

.request-resources h2 {
    margin-bottom: 20px;
}

.request-resources .request-info p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

/* Contributor Dashboard Styles */
.contributor-dashboard .dashboard-header {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 40px 0;
}

.contributor-dashboard .dashboard-header h1 {
    margin-bottom: 10px;
    font-size: 2.2rem;
}

.contributor-dashboard .dashboard-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 30px;
}

.user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.user-details {
    line-height: 1.6;
}

.dashboard-actions {
    display: flex;
    gap: 10px;
}

.dashboard-actions .btn-secondary,
.dashboard-actions .btn-tertiary {
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.dashboard-actions .btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.dashboard-actions .btn-tertiary {
    background: transparent;
    color: white;
    border: 1px solid rgba(255,255,255,0.5);
}

.dashboard-content {
    padding: 50px 0;
}

/* Dashboard Stats */
.dashboard-stats {
    margin-bottom: 50px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-info h3 {
    color: #1976d2;
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.stat-info p {
    margin: 2px 0;
    color: #666;
    font-size: 0.9rem;
}

/* Submission Forms */
.submission-forms {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 20px;
    margin-bottom: 50px;
}

.forms-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 12px 20px;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    color: #666;
    transition: all 0.3s ease;
}

.tab-btn:hover,
.tab-btn.active {
    background: #1976d2;
    color: white;
    border-color: #1976d2;
}

.submission-form {
    display: none;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.submission-form.active {
    display: block;
}

.submission-form h3 {
    color: #1976d2;
    margin-bottom: 25px;
    font-size: 1.4rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1976d2;
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 0.85rem;
}

.form-group .btn-primary {
    background: #1976d2;
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.form-group .btn-primary:hover {
    background: #1565c0;
    transform: translateY(-2px);
}

.form-group .btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Recent Submissions */
.recent-submissions {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.submissions-list {
    margin-top: 30px;
}

.submission-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.submission-item.pending {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
}

.submission-item.approved {
    background: #d1edff;
    border-left: 4px solid #28a745;
}

.submission-item:hover {
    transform: translateX(5px);
}

.submission-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

.submission-details h4 {
    color: #1976d2;
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.submission-meta {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.submission-type {
    font-weight: 600;
}

.submission-status {
    font-weight: 600;
}

.status-pending {
    color: #856404;
}

.status-approved {
    color: #155724;
}

.no-submissions {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px;
}

/* Events Page Styles */
.events-page .page-header {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.events-filter {
    margin-top: 30px;
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.4);
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.event-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.event-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.event-card.priority-high {
    border: 2px solid #ff5722;
    background: linear-gradient(135deg, #fff, #ffebee);
}

.event-card.priority-medium {
    border: 2px solid #ff9800;
}

.event-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.priority-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #ff5722;
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.event-content {
    padding: 25px;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.event-title {
    color: #1976d2;
    margin: 0;
    font-size: 1.3rem;
    line-height: 1.3;
    flex: 1;
}

.event-department {
    background: #1976d2;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 10px;
}

.event-datetime {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
}

.event-date,
.event-time {
    margin-bottom: 5px;
}

.event-location {
    color: #666;
    margin-bottom: 15px;
}

.event-description {
    color: #333;
    line-height: 1.6;
    margin-bottom: 20px;
}

.event-details {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.event-cost {
    margin-bottom: 10px;
}

.event-cost.free {
    color: #28a745;
}

.registration-required {
    color: #dc3545;
    font-weight: 600;
}

.event-contact {
    background: #e3f2fd;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.event-contact h4 {
    color: #1976d2;
    margin-bottom: 10px;
    font-size: 1rem;
}

.event-contact p {
    margin: 5px 0;
    color: #333;
}

.event-contact a {
    color: #1976d2;
    text-decoration: none;
}

.event-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.event-actions .btn-primary,
.event-actions .btn-secondary,
.event-actions .btn-tertiary {
    padding: 10px 15px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    flex: 1;
    min-width: 120px;
    font-size: 0.9rem;
    border: none;
    cursor: pointer;
}

.event-actions .btn-primary {
    background: #1976d2;
    color: white;
}

.event-actions .btn-secondary {
    background: #28a745;
    color: white;
}

.event-actions .btn-tertiary {
    background: #f5f5f5;
    color: #666;
    border: 2px solid #e0e0e0;
}

.event-actions .btn-primary:hover {
    background: #1565c0;
}

.event-actions .btn-secondary:hover {
    background: #218838;
}

.event-actions .btn-tertiary:hover {
    background: #e0e0e0;
}

.no-events {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    font-style: italic;
}

/* Past Events and Schedule Styles */
.past-events,
.regular-schedule,
.events-contact {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 20px;
    margin: 50px 0;
}

.past-events-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.past-event-item {
    display: flex;
    align-items: center;
    gap: 20px;
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.past-event-date {
    background: #1976d2;
    color: white;
    padding: 10px;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    min-width: 60px;
}

.past-event-info h4 {
    color: #1976d2;
    margin-bottom: 5px;
}

.past-event-department {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.schedule-item {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.schedule-item h3 {
    color: #1976d2;
    margin-bottom: 10px;
}

.contact-options {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.contact-btn {
    padding: 12px 24px;
    background: #1976d2;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.contact-btn:hover {
    background: #1565c0;
    transform: translateY(-2px);
}

/* Event Modal Styles */
.event-modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.modal-close:hover {
    color: #000;
}

/* Events Management Dashboard Styles */
.events-management .management-header {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 40px 0;
}

.events-management .management-header h1 {
    margin-bottom: 10px;
    font-size: 2.2rem;
}

.events-management .management-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 30px;
}

.management-content {
    padding: 50px 0;
}

.management-stats {
    margin-bottom: 50px;
}

.event-management-interface {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 20px;
}

.interface-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.interface-header h2 {
    color: #1976d2;
    margin: 0;
}

.event-form-container {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.form-header h3 {
    color: #1976d2;
    margin: 0;
}

.form-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 5px;
}

.form-close:hover {
    color: #333;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.events-table-container {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.events-table {
    width: 100%;
    border-collapse: collapse;
}

.events-table th,
.events-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.events-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.events-table tr:hover {
    background: #f8f9fa;
}

.category-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: capitalize;
}

.category-worship { background: #e3f2fd; color: #1976d2; }
.category-fellowship { background: #f3e5f5; color: #7b1fa2; }
.category-outreach { background: #e8f5e8; color: #388e3c; }
.category-youth { background: #fff3e0; color: #f57c00; }
.category-health { background: #e0f2f1; color: #00796b; }
.category-education { background: #fce4ec; color: #c2185b; }
.category-special { background: #ffebee; color: #d32f2f; }

.priority-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: capitalize;
}

.priority-badge.priority-high {
    background: #ffebee;
    color: #d32f2f;
}

.priority-badge.priority-medium {
    background: #fff3e0;
    color: #f57c00;
}

.priority-badge:not(.priority-high):not(.priority-medium) {
    background: #f5f5f5;
    color: #666;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
    border-radius: 15px;
    border: none;
    cursor: pointer;
    margin-right: 5px;
    transition: all 0.3s ease;
}

.btn-small:hover {
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.ministry-card:hover {
    transform: translateY(-5px);
}
