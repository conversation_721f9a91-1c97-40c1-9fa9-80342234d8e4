/*
Theme Name: Astra Agape Child
Description: Child theme of Astra optimized for Agape SDA Church Vanderbijlpark with enhanced SEO features
Author: Agape SDA Church
Template: astra
Version: 1.0.0
Text Domain: astra-agape-child
*/

/* Import parent theme styles */
@import url("../astra/style.css");

/* Custom styles for Agape SDA Church */
.church-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.service-times {
    background: #e3f2fd;
    padding: 15px;
    border-left: 4px solid #1976d2;
    margin: 15px 0;
}

.church-contact {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 20px 0;
}

.contact-item {
    flex: 1;
    min-width: 200px;
    padding: 15px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sda-highlight {
    color: #1976d2;
    font-weight: 600;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .church-contact {
        flex-direction: column;
    }
    
    .contact-item {
        min-width: 100%;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Church-specific styling */
.sabbath-emphasis {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin: 20px 0;
}

.ministry-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.ministry-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.ministry-card:hover {
    transform: translateY(-5px);
}
