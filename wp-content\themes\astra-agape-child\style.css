/*
Theme Name: Astra Agape Child
Description: Child theme of Astra optimized for Agape SDA Church Vanderbijlpark with enhanced SEO features
Author: Agape SDA Church
Template: astra
Version: 1.0.0
Text Domain: astra-agape-child
*/

/* Import parent theme styles */
@import url("../astra/style.css");

/* Enhanced styles for Agape SDA Church */

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-content h1 small {
    font-size: 1.2rem;
    font-weight: 400;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 30px;
    line-height: 1.6;
}

.cta-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin: 30px 0;
}

.btn-primary, .btn-secondary, .btn-tertiary {
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: #fff;
    color: #1976d2;
}

.btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.btn-tertiary {
    background: rgba(255,255,255,0.2);
    color: #fff;
}

.btn-primary:hover {
    background: #f5f5f5;
    transform: translateY(-2px);
}

.btn-secondary:hover, .btn-tertiary:hover {
    background: rgba(255,255,255,0.1);
    transform: translateY(-2px);
}

.quick-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 40px;
    font-size: 0.95rem;
}

.info-item {
    background: rgba(255,255,255,0.1);
    padding: 10px 15px;
    border-radius: 20px;
}

/* Live Service Banner */
.live-service-banner {
    background: #f8f9fa;
    padding: 40px 0;
}

.service-schedule {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin: 20px 0;
}

.schedule-item {
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    min-width: 200px;
}

.welcome-message {
    text-align: center;
    font-style: italic;
    color: #666;
    margin-top: 20px;
}

/* Benefits Grid */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.benefit-item {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-5px);
}

.benefit-item h3 {
    color: #1976d2;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

/* Contact Grid */
.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.contact-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-3px);
}

.contact-card h3 {
    color: #1976d2;
    margin-bottom: 15px;
}

.btn-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    margin-top: 10px;
}

.btn-link:hover {
    text-decoration: underline;
}

.sda-highlight {
    color: #1976d2;
    font-weight: 600;
}

/* Beliefs Section */
.beliefs-comparison {
    margin: 30px 0;
}

.belief-highlight {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 30px;
}

.beliefs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.belief-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #1976d2;
}

.belief-card h4 {
    color: #1976d2;
    margin-bottom: 10px;
}

/* Ministry Cards */
.ministry-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.ministry-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.ministry-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.ministry-card.featured {
    border: 2px solid #1976d2;
    background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
}

.ministry-card h3 {
    color: #1976d2;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.card-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    margin-top: 15px;
    padding: 8px 16px;
    border: 1px solid #1976d2;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.card-link:hover {
    background: #1976d2;
    color: white;
}

.ministry-cta {
    text-align: center;
    margin-top: 40px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 15px;
}

/* Testimonials */
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.testimonial-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative;
}

.testimonial-card::before {
    content: '"';
    font-size: 4rem;
    color: #1976d2;
    position: absolute;
    top: -10px;
    left: 20px;
    opacity: 0.3;
}

.stars {
    color: #ffc107;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.testimonial-card cite {
    font-style: italic;
    color: #666;
    font-weight: 600;
}

/* Community Stats */
.community-stats {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 15px;
    margin: 40px 0;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

.stat-item {
    text-align: center;
}

.stat-item strong {
    display: block;
    font-size: 2.5rem;
    color: #1976d2;
    font-weight: 700;
}

.stat-item span {
    color: #666;
    font-size: 0.9rem;
}

/* Events Section */
.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.event-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    transition: transform 0.3s ease;
}

.event-card:hover {
    transform: translateY(-3px);
}

.event-card.featured {
    border: 2px solid #1976d2;
}

.event-date {
    background: #1976d2;
    color: white;
    padding: 20px;
    text-align: center;
    min-width: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.event-date .day {
    font-size: 1.8rem;
    font-weight: 700;
}

.event-date .month {
    font-size: 0.9rem;
    text-transform: uppercase;
}

.event-info {
    padding: 20px;
    flex: 1;
}

.event-info h3 {
    color: #1976d2;
    margin-bottom: 10px;
}

.event-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    margin-top: 10px;
}

.events-cta {
    text-align: center;
    margin-top: 30px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .quick-info {
        flex-direction: column;
        gap: 15px;
    }

    .service-schedule {
        flex-direction: column;
        gap: 15px;
    }

    .schedule-item {
        min-width: auto;
    }

    .benefits-grid,
    .contact-grid,
    .beliefs-grid,
    .ministry-grid,
    .testimonials-grid,
    .stats-grid,
    .events-grid {
        grid-template-columns: 1fr;
    }

    .event-card {
        flex-direction: column;
    }

    .event-date {
        min-width: auto;
        flex-direction: row;
        justify-content: center;
        gap: 10px;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Church-specific styling */
.sabbath-emphasis {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin: 20px 0;
}

.ministry-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.ministry-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

/* Location Section */
.location-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

.location-info, .directions-info {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.location-info h3, .directions-info h3 {
    color: #1976d2;
    margin-bottom: 15px;
}

.location-info ul {
    list-style: none;
    padding: 0;
}

.location-info li {
    padding: 5px 0;
    color: #333;
}

/* Final CTA Section */
.final-cta-section {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.2rem;
    margin-bottom: 15px;
}

.cta-subtitle {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin: 40px 0;
}

.cta-option {
    background: rgba(255,255,255,0.1);
    padding: 30px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.cta-option.primary {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
}

.cta-option h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.cta-option p {
    margin-bottom: 20px;
    opacity: 0.9;
}

.final-message {
    margin-top: 50px;
    padding-top: 30px;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.final-message h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    font-style: italic;
}

.social-proof {
    margin-top: 20px;
    opacity: 0.8;
}

.ministry-card:hover {
    transform: translateY(-5px);
}
