<?php
/**
 * Events Announcement Page Template for Agape SDA Church
 * Public page displaying church events and announcements
 */

get_header(); ?>

<main id="main" class="site-main events-page" role="main">
    
    <article class="page-content" itemscope itemtype="https://schema.org/WebPage">
        
        <header class="page-header">
            <div class="container">
                <h1 class="page-title" itemprop="name">Church Events & Announcements</h1>
                <p class="page-subtitle">Stay updated with upcoming events and activities at Agape SDA Church Vanderbijlpark</p>
                
                <div class="events-filter">
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-category="all">All Events</button>
                        <button class="filter-btn" data-category="worship">Worship Services</button>
                        <button class="filter-btn" data-category="fellowship">Fellowship</button>
                        <button class="filter-btn" data-category="outreach">Community Outreach</button>
                        <button class="filter-btn" data-category="youth">Youth Programs</button>
                        <button class="filter-btn" data-category="health">Health Ministry</button>
                        <button class="filter-btn" data-category="education">Education</button>
                        <button class="filter-btn" data-category="special">Special Events</button>
                    </div>
                </div>
            </div>
        </header>

        <section class="events-content" itemprop="mainContentOfPage">
            <div class="container">
                
                <!-- Upcoming Events -->
                <div class="upcoming-events">
                    <h2>📅 Upcoming Events</h2>
                    <div class="events-grid" id="events-container">
                        
                        <?php
                        // Get upcoming events
                        $today = date('Y-m-d');
                        $upcoming_events = get_posts(array(
                            'post_type' => 'church_event',
                            'post_status' => 'publish',
                            'numberposts' => -1,
                            'meta_query' => array(
                                array(
                                    'key' => '_event_date',
                                    'value' => $today,
                                    'compare' => '>='
                                )
                            ),
                            'meta_key' => '_event_date',
                            'orderby' => 'meta_value',
                            'order' => 'ASC'
                        ));
                        
                        if ($upcoming_events) {
                            foreach ($upcoming_events as $event) {
                                $event_date = get_post_meta($event->ID, '_event_date', true);
                                $event_time = get_post_meta($event->ID, '_event_time', true);
                                $event_end_date = get_post_meta($event->ID, '_event_end_date', true);
                                $event_end_time = get_post_meta($event->ID, '_event_end_time', true);
                                $event_location = get_post_meta($event->ID, '_event_location', true);
                                $event_department = get_post_meta($event->ID, '_event_department', true);
                                $contact_person = get_post_meta($event->ID, '_event_contact_person', true);
                                $contact_phone = get_post_meta($event->ID, '_event_contact_phone', true);
                                $contact_email = get_post_meta($event->ID, '_event_contact_email', true);
                                $registration_required = get_post_meta($event->ID, '_event_registration_required', true);
                                $max_attendees = get_post_meta($event->ID, '_event_max_attendees', true);
                                $event_cost = get_post_meta($event->ID, '_event_cost', true);
                                $event_category = get_post_meta($event->ID, '_event_category', true);
                                $event_priority = get_post_meta($event->ID, '_event_priority', true);
                                
                                $priority_class = $event_priority === 'high' ? 'priority-high' : ($event_priority === 'medium' ? 'priority-medium' : 'priority-normal');
                                ?>
                                
                                <div class="event-card <?php echo $priority_class; ?>" data-category="<?php echo esc_attr($event_category); ?>" itemscope itemtype="https://schema.org/Event">
                                    <?php if (has_post_thumbnail($event->ID)) : ?>
                                        <div class="event-image">
                                            <?php echo get_the_post_thumbnail($event->ID, 'medium', array('itemprop' => 'image')); ?>
                                            <?php if ($event_priority === 'high') : ?>
                                                <div class="priority-badge">🔥 Important</div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="event-content">
                                        <div class="event-header">
                                            <h3 class="event-title" itemprop="name"><?php echo esc_html($event->post_title); ?></h3>
                                            <?php if ($event_department) : ?>
                                                <span class="event-department"><?php echo esc_html($event_department); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="event-datetime" itemprop="startDate" content="<?php echo $event_date . 'T' . $event_time; ?>">
                                            <div class="event-date">
                                                <strong>📅 <?php echo date('l, F j, Y', strtotime($event_date)); ?></strong>
                                            </div>
                                            <div class="event-time">
                                                <strong>🕐 <?php echo date('g:i A', strtotime($event_time)); ?></strong>
                                                <?php if ($event_end_time) : ?>
                                                    - <?php echo date('g:i A', strtotime($event_end_time)); ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        
                                        <?php if ($event_location) : ?>
                                            <div class="event-location" itemprop="location" itemscope itemtype="https://schema.org/Place">
                                                <strong>📍 <span itemprop="name"><?php echo esc_html($event_location); ?></span></strong>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="event-description" itemprop="description">
                                            <?php if ($event->post_excerpt) : ?>
                                                <p><?php echo esc_html($event->post_excerpt); ?></p>
                                            <?php else : ?>
                                                <p><?php echo wp_trim_words($event->post_content, 30); ?></p>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="event-details">
                                            <?php if ($event_cost && $event_cost !== 'Free') : ?>
                                                <div class="event-cost">
                                                    <strong>💰 Cost:</strong> <?php echo esc_html($event_cost); ?>
                                                </div>
                                            <?php elseif ($event_cost === 'Free') : ?>
                                                <div class="event-cost free">
                                                    <strong>🆓 Free Event</strong>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($registration_required === 'yes') : ?>
                                                <div class="registration-required">
                                                    <strong>📝 Registration Required</strong>
                                                    <?php if ($max_attendees) : ?>
                                                        <br><small>Limited to <?php echo esc_html($max_attendees); ?> attendees</small>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <?php if ($contact_person || $contact_phone || $contact_email) : ?>
                                            <div class="event-contact">
                                                <h4>📞 Contact Information</h4>
                                                <?php if ($contact_person) : ?>
                                                    <p><strong>Contact:</strong> <?php echo esc_html($contact_person); ?></p>
                                                <?php endif; ?>
                                                <?php if ($contact_phone) : ?>
                                                    <p><strong>Phone:</strong> <a href="tel:<?php echo esc_attr($contact_phone); ?>"><?php echo esc_html($contact_phone); ?></a></p>
                                                <?php endif; ?>
                                                <?php if ($contact_email) : ?>
                                                    <p><strong>Email:</strong> <a href="mailto:<?php echo esc_attr($contact_email); ?>"><?php echo esc_html($contact_email); ?></a></p>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="event-actions">
                                            <button class="btn-primary event-details-btn" data-event-id="<?php echo $event->ID; ?>">
                                                📖 View Full Details
                                            </button>
                                            <?php if ($registration_required === 'yes') : ?>
                                                <a href="tel:+27685180531" class="btn-secondary">📞 Register Now</a>
                                            <?php endif; ?>
                                            <button class="btn-tertiary add-to-calendar" data-event-id="<?php echo $event->ID; ?>">
                                                📅 Add to Calendar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php
                            }
                        } else {
                            echo '<div class="no-events"><p>No upcoming events at this time. Check back soon for new announcements!</p></div>';
                        }
                        ?>
                        
                    </div>
                </div>

                <!-- Past Events -->
                <div class="past-events">
                    <h2>📋 Recent Past Events</h2>
                    <div class="past-events-list">
                        <?php
                        $past_events = get_posts(array(
                            'post_type' => 'church_event',
                            'post_status' => 'publish',
                            'numberposts' => 6,
                            'meta_query' => array(
                                array(
                                    'key' => '_event_date',
                                    'value' => $today,
                                    'compare' => '<'
                                )
                            ),
                            'meta_key' => '_event_date',
                            'orderby' => 'meta_value',
                            'order' => 'DESC'
                        ));
                        
                        if ($past_events) {
                            foreach ($past_events as $event) {
                                $event_date = get_post_meta($event->ID, '_event_date', true);
                                $event_department = get_post_meta($event->ID, '_event_department', true);
                                ?>
                                <div class="past-event-item">
                                    <div class="past-event-date">
                                        <?php echo date('M j', strtotime($event_date)); ?>
                                    </div>
                                    <div class="past-event-info">
                                        <h4><?php echo esc_html($event->post_title); ?></h4>
                                        <?php if ($event_department) : ?>
                                            <span class="past-event-department"><?php echo esc_html($event_department); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php
                            }
                        } else {
                            echo '<p>No past events to display.</p>';
                        }
                        ?>
                    </div>
                </div>

                <!-- Regular Schedule -->
                <div class="regular-schedule">
                    <h2>📆 Regular Church Schedule</h2>
                    <div class="schedule-grid">
                        <div class="schedule-item">
                            <h3>🎵 Sabbath Worship</h3>
                            <p><strong>Every Saturday</strong></p>
                            <p>9:00 AM - 12:00 PM</p>
                            <p>Main Sanctuary</p>
                        </div>
                        
                        <div class="schedule-item">
                            <h3>📖 Bible Study</h3>
                            <p><strong>Every Wednesday</strong></p>
                            <p>7:00 PM - 8:30 PM</p>
                            <p>Fellowship Hall</p>
                        </div>
                        
                        <div class="schedule-item">
                            <h3>🙏 Prayer Meeting</h3>
                            <p><strong>Every Wednesday</strong></p>
                            <p>7:30 PM - 8:30 PM</p>
                            <p>Prayer Room</p>
                        </div>
                        
                        <div class="schedule-item">
                            <h3>👨‍👩‍👧‍👦 Youth Programs</h3>
                            <p><strong>Every Saturday</strong></p>
                            <p>2:00 PM - 4:00 PM</p>
                            <p>Youth Hall</p>
                        </div>
                    </div>
                </div>

                <!-- Contact for Events -->
                <div class="events-contact">
                    <h2>📞 Event Information & Registration</h2>
                    <div class="contact-info">
                        <p>For more information about any event or to register for activities, please contact:</p>
                        <div class="contact-options">
                            <a href="tel:+27685180531" class="contact-btn">📞 +27 68 518 0531</a>
                            <a href="mailto:<EMAIL>" class="contact-btn">✉️ <EMAIL></a>
                            <?php echo do_shortcode('[whatsapp_contact text="💬 WhatsApp"]'); ?>
                        </div>
                    </div>
                </div>

            </div>
        </section>
        
    </article>

</main>

<!-- Event Details Modal -->
<div id="event-modal" class="event-modal" style="display: none;">
    <div class="modal-content">
        <span class="modal-close">&times;</span>
        <div id="modal-event-content">
            <!-- Event details will be loaded here -->
        </div>
    </div>
</div>

<script>
// Events page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const filterBtns = document.querySelectorAll('.filter-btn');
    const eventCards = document.querySelectorAll('.event-card');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter events
            eventCards.forEach(card => {
                const cardCategory = card.dataset.category;
                
                if (category === 'all' || cardCategory === category) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
    
    // Event details modal
    const modal = document.getElementById('event-modal');
    const modalContent = document.getElementById('modal-event-content');
    const closeBtn = document.querySelector('.modal-close');
    const detailsBtns = document.querySelectorAll('.event-details-btn');
    
    detailsBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const eventId = this.dataset.eventId;
            // In a real implementation, you would fetch full event details via AJAX
            // For now, we'll show the event card content in the modal
            const eventCard = this.closest('.event-card');
            modalContent.innerHTML = eventCard.innerHTML;
            modal.style.display = 'block';
        });
    });
    
    closeBtn.addEventListener('click', function() {
        modal.style.display = 'none';
    });
    
    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    // Add to calendar functionality
    const calendarBtns = document.querySelectorAll('.add-to-calendar');
    calendarBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const eventCard = this.closest('.event-card');
            const title = eventCard.querySelector('.event-title').textContent;
            const dateTime = eventCard.querySelector('[itemprop="startDate"]').getAttribute('content');
            
            // Create calendar URL (Google Calendar)
            const calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(title)}&dates=${dateTime.replace(/[-:]/g, '')}/${dateTime.replace(/[-:]/g, '')}`;
            
            window.open(calendarUrl, '_blank');
        });
    });
});
</script>

<?php get_footer(); ?>
