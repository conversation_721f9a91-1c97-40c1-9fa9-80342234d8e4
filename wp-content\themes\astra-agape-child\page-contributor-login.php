<?php
/**
 * Contributor Login Page Template for Agape SDA Church
 * Secure login for content contributors
 */

// Redirect if already logged in
if (is_user_logged_in()) {
    wp_redirect(home_url('/contributor-dashboard'));
    exit;
}

get_header(); ?>

<main id="main" class="site-main contributor-login" role="main">
    
    <div class="login-container">
        <div class="container">
            
            <div class="login-card">
                <div class="login-header">
                    <h1>🔐 Contributor Login</h1>
                    <p>Access the Agape SDA Church content submission system</p>
                </div>

                <div class="login-form-container">
                    <?php
                    // Display any error messages
                    if (isset($_GET['login']) && $_GET['login'] === 'failed') {
                        echo '<div class="login-error">❌ Invalid username or password. Please try again.</div>';
                    }
                    
                    if (isset($_GET['login']) && $_GET['login'] === 'empty') {
                        echo '<div class="login-error">❌ Please enter both username and password.</div>';
                    }
                    
                    if (isset($_GET['action']) && $_GET['action'] === 'register' && isset($_GET['registered']) && $_GET['registered'] === 'true') {
                        echo '<div class="login-success">✅ Registration successful! Please log in with your credentials.</div>';
                    }
                    ?>

                    <form name="loginform" id="loginform" action="<?php echo esc_url(site_url('wp-login.php', 'login_post')); ?>" method="post">
                        <div class="form-group">
                            <label for="user_login">👤 Username or Email</label>
                            <input type="text" name="log" id="user_login" class="input" value="" size="20" autocapitalize="off" required />
                        </div>
                        
                        <div class="form-group">
                            <label for="user_pass">🔒 Password</label>
                            <input type="password" name="pwd" id="user_pass" class="input" value="" size="20" required />
                        </div>
                        
                        <div class="form-group checkbox-group">
                            <label for="rememberme">
                                <input name="rememberme" type="checkbox" id="rememberme" value="forever" />
                                Remember Me
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <input type="submit" name="wp-submit" id="wp-submit" class="btn-primary" value="🚪 Log In" />
                            <input type="hidden" name="redirect_to" value="<?php echo esc_attr(home_url('/contributor-dashboard')); ?>" />
                        </div>
                    </form>

                    <div class="login-links">
                        <a href="<?php echo wp_lostpassword_url(); ?>">🔑 Forgot Password?</a>
                        <span class="separator">|</span>
                        <a href="#register" class="show-register">📝 Request Access</a>
                    </div>
                </div>

                <!-- Registration Request Form -->
                <div class="register-form-container" id="register-form" style="display: none;">
                    <h2>📝 Request Contributor Access</h2>
                    <p>Fill out this form to request access to the content submission system. Your request will be reviewed by the church administration.</p>
                    
                    <form id="access-request-form">
                        <div class="form-group">
                            <label for="req_name">Full Name *</label>
                            <input type="text" id="req_name" name="name" required />
                        </div>
                        
                        <div class="form-group">
                            <label for="req_email">Email Address *</label>
                            <input type="email" id="req_email" name="email" required />
                        </div>
                        
                        <div class="form-group">
                            <label for="req_phone">Phone Number</label>
                            <input type="tel" id="req_phone" name="phone" />
                        </div>
                        
                        <div class="form-group">
                            <label for="req_role">Church Role/Ministry</label>
                            <select id="req_role" name="role">
                                <option value="">Select your role</option>
                                <option value="pastor">Pastor/Elder</option>
                                <option value="deacon">Deacon/Deaconess</option>
                                <option value="teacher">Sabbath School Teacher</option>
                                <option value="youth-leader">Youth Leader</option>
                                <option value="health-ministry">Health Ministry</option>
                                <option value="music-ministry">Music Ministry</option>
                                <option value="media-ministry">Media Ministry</option>
                                <option value="member">Church Member</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="req_content_type">What type of content would you like to contribute?</label>
                            <div class="checkbox-group">
                                <label><input type="checkbox" name="content_types[]" value="blog"> Blog Posts</label>
                                <label><input type="checkbox" name="content_types[]" value="sermons"> Sermons</label>
                                <label><input type="checkbox" name="content_types[]" value="presentations"> Presentations</label>
                                <label><input type="checkbox" name="content_types[]" value="videos"> Videos</label>
                                <label><input type="checkbox" name="content_types[]" value="books"> Books/Literature</label>
                                <label><input type="checkbox" name="content_types[]" value="other"> Other Resources</label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="req_experience">Brief description of your experience and why you'd like to contribute</label>
                            <textarea id="req_experience" name="experience" rows="4" placeholder="Tell us about your background and what you'd like to share with the church community..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn-primary">📤 Submit Request</button>
                            <button type="button" class="btn-secondary show-login">🔙 Back to Login</button>
                        </div>
                    </form>
                </div>

                <div class="login-info">
                    <h3>ℹ️ About Contributor Access</h3>
                    <div class="info-content">
                        <p><strong>Who can become a contributor?</strong></p>
                        <ul>
                            <li>Church members in good standing</li>
                            <li>Ministry leaders and volunteers</li>
                            <li>Approved guest speakers and authors</li>
                            <li>Community partners (with approval)</li>
                        </ul>
                        
                        <p><strong>What can contributors do?</strong></p>
                        <ul>
                            <li>Submit blog posts for the church website</li>
                            <li>Upload sermon recordings and notes</li>
                            <li>Share presentations and study materials</li>
                            <li>Contribute video resources and documentaries</li>
                            <li>Add books and literature to the library</li>
                        </ul>
                        
                        <p><strong>Content Review Process:</strong></p>
                        <ul>
                            <li>All submissions are reviewed before publication</li>
                            <li>Content must align with SDA beliefs and values</li>
                            <li>Contributors receive notification of approval/rejection</li>
                            <li>Approved content is published on the website</li>
                        </ul>
                    </div>
                </div>

                <div class="contact-admin">
                    <h3>📞 Need Help?</h3>
                    <p>Contact the church administration for assistance:</p>
                    <div class="contact-options">
                        <a href="tel:+27685180531" class="contact-link">📞 +27 68 518 0531</a>
                        <a href="mailto:<EMAIL>" class="contact-link">✉️ <EMAIL></a>
                        <?php echo do_shortcode('[whatsapp_contact text="💬 WhatsApp"]'); ?>
                    </div>
                </div>

            </div>
            
        </div>
    </div>

</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const showRegisterBtn = document.querySelector('.show-register');
    const showLoginBtn = document.querySelector('.show-login');
    const loginForm = document.querySelector('.login-form-container');
    const registerForm = document.querySelector('.register-form-container');
    const accessRequestForm = document.getElementById('access-request-form');
    
    // Toggle between login and registration forms
    showRegisterBtn.addEventListener('click', function(e) {
        e.preventDefault();
        loginForm.style.display = 'none';
        registerForm.style.display = 'block';
    });
    
    showLoginBtn.addEventListener('click', function(e) {
        e.preventDefault();
        registerForm.style.display = 'none';
        loginForm.style.display = 'block';
    });
    
    // Handle access request form submission
    accessRequestForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        submitBtn.textContent = '⏳ Submitting...';
        submitBtn.disabled = true;
        
        // Collect checkbox values
        const contentTypes = [];
        const checkboxes = this.querySelectorAll('input[name="content_types[]"]:checked');
        checkboxes.forEach(cb => contentTypes.push(cb.value));
        
        // Prepare email content
        const emailData = {
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            role: formData.get('role'),
            content_types: contentTypes.join(', '),
            experience: formData.get('experience')
        };
        
        // Send email to admin (this would typically be handled by a server-side script)
        // For now, we'll show a success message and provide contact information
        setTimeout(() => {
            alert('✅ Your access request has been submitted! You will receive a response within 2-3 business days. For urgent requests, please contact the church directly.');
            this.reset();
            registerForm.style.display = 'none';
            loginForm.style.display = 'block';
            
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
});
</script>

<style>
/* Login Page Specific Styles */
.contributor-login {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 50px 0;
}

.login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
}

.login-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 40px;
    max-width: 500px;
    width: 100%;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    color: #1976d2;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.login-header p {
    color: #666;
    margin: 0;
}

.login-error {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
}

.login-success {
    background: #d4edda;
    color: #155724;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #c3e6cb;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1976d2;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #1976d2;
    color: white;
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
    border: 2px solid #e0e0e0;
}

.btn-primary:hover {
    background: #1565c0;
    transform: translateY(-2px);
}

.btn-secondary:hover {
    background: #e0e0e0;
}

.login-links {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.login-links a {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
}

.login-links .separator {
    margin: 0 10px;
    color: #ccc;
}

.login-info,
.contact-admin {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.login-info h3,
.contact-admin h3 {
    color: #1976d2;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.info-content ul {
    margin: 10px 0;
    padding-left: 20px;
}

.info-content li {
    margin-bottom: 5px;
    color: #666;
}

.contact-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.contact-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 600;
    padding: 8px 12px;
    background: white;
    border-radius: 20px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.contact-link:hover {
    background: #e3f2fd;
    border-color: #1976d2;
}

@media (max-width: 768px) {
    .login-card {
        margin: 20px;
        padding: 30px 20px;
    }
    
    .contact-options {
        flex-direction: column;
    }
    
    .contact-link {
        text-align: center;
    }
}
</style>

<?php get_footer(); ?>
