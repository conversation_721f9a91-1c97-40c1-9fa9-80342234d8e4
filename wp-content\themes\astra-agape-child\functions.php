<?php
/**
 * Astra Agape Child Theme Functions
 * Enhanced SEO and functionality for Agape SDA Church Vanderbijlpark
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Enqueue parent and child theme styles
function astra_agape_child_enqueue_styles() {
    wp_enqueue_style('astra-parent-style', get_template_directory_uri() . '/style.css');
    wp_enqueue_style('astra-agape-child-style', get_stylesheet_directory_uri() . '/style.css', array('astra-parent-style'), '1.0.0');
}
add_action('wp_enqueue_scripts', 'astra_agape_child_enqueue_styles');

/**
 * Enhanced Church Schema Markup for Agape SDA Church
 */
function agape_enhanced_church_schema() {
    if (is_front_page()) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Church',
            'name' => 'Agape SDA Church Vanderbijlpark',
            'alternateName' => array(
                'Agape Seventh-day Adventist Church',
                'Agape SDA Vanderbijlpark',
                'SDA Church Vaal Triangle'
            ),
            'description' => 'Seventh-day Adventist church serving the Vanderbijlpark and Vaal Triangle community. Join us for Sabbath worship, Bible study, and community outreach programs.',
            'address' => array(
                '@type' => 'PostalAddress',
                'streetAddress' => '1 Gillchrist Street C.W.6',
                'addressLocality' => 'Vanderbijlpark',
                'addressRegion' => 'Gauteng',
                'postalCode' => '1900',
                'addressCountry' => 'ZA'
            ),
            'geo' => array(
                '@type' => 'GeoCoordinates',
                'latitude' => -26.68847,
                'longitude' => 27.84492
            ),
            'url' => home_url(),
            'email' => '<EMAIL>',
            'telephone' => '+27685180531',
            'serviceType' => array(
                'Seventh-day Adventist church services',
                'Sabbath worship',
                'Bible study',
                'Community outreach',
                'Health ministry'
            ),
            'openingHours' => array(
                'Sa 09:00-12:00'
            ),
            'denomination' => 'Seventh-day Adventist',
            'foundingDate' => '1980',
            'areaServed' => array(
                'Vanderbijlpark',
                'Vaal Triangle',
                'Gauteng',
                'South Africa'
            ),
            'sameAs' => array(
                'https://www.adventist.org',
                'https://adventist.org.za'
            ),
            'hasOfferCatalog' => array(
                '@type' => 'OfferCatalog',
                'name' => 'Church Services and Ministries',
                'itemListElement' => array(
                    array(
                        '@type' => 'Offer',
                        'itemOffered' => array(
                            '@type' => 'Service',
                            'name' => 'Sabbath Worship Service',
                            'description' => 'Weekly Sabbath worship service every Saturday from 9:00 AM to 12:00 PM',
                            'serviceType' => 'Religious Service'
                        )
                    ),
                    array(
                        '@type' => 'Offer',
                        'itemOffered' => array(
                            '@type' => 'Service',
                            'name' => 'Bible Study',
                            'description' => 'Weekly Bible study sessions and prayer meetings',
                            'serviceType' => 'Educational Service'
                        )
                    ),
                    array(
                        '@type' => 'Offer',
                        'itemOffered' => array(
                            '@type' => 'Service',
                            'name' => 'Community Outreach',
                            'description' => 'Community service programs and health ministry',
                            'serviceType' => 'Community Service'
                        )
                    ),
                    array(
                        '@type' => 'Offer',
                        'itemOffered' => array(
                            '@type' => 'Service',
                            'name' => 'Youth Ministry',
                            'description' => 'Programs and activities for young people',
                            'serviceType' => 'Youth Service'
                        )
                    )
                )
            ),
            'event' => array(
                '@type' => 'Event',
                'name' => 'Sabbath Worship Service',
                'description' => 'Join us every Sabbath for worship, fellowship, and spiritual growth',
                'startDate' => date('Y-m-d', strtotime('next saturday')) . 'T09:00:00',
                'endDate' => date('Y-m-d', strtotime('next saturday')) . 'T12:00:00',
                'eventSchedule' => array(
                    '@type' => 'Schedule',
                    'repeatFrequency' => 'P1W',
                    'byDay' => 'Saturday'
                ),
                'location' => array(
                    '@type' => 'Place',
                    'name' => 'Agape SDA Church Vanderbijlpark',
                    'address' => array(
                        '@type' => 'PostalAddress',
                        'streetAddress' => '1 Gillchrist Street C.W.6',
                        'addressLocality' => 'Vanderbijlpark',
                        'addressRegion' => 'Gauteng',
                        'postalCode' => '1900',
                        'addressCountry' => 'ZA'
                    )
                ),
                'organizer' => array(
                    '@type' => 'Organization',
                    'name' => 'Agape SDA Church Vanderbijlpark'
                )
            )
        );
        
        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>' . "\n";
    }
}
add_action('wp_head', 'agape_enhanced_church_schema', 1);

/**
 * Enhanced Local SEO Meta Tags
 */
function agape_local_seo_meta() {
    if (is_front_page()) {
        // Geographic meta tags
        echo '<meta name="geo.region" content="ZA-GP" />' . "\n";
        echo '<meta name="geo.placename" content="Vanderbijlpark, Gauteng, South Africa" />' . "\n";
        echo '<meta name="geo.position" content="-26.68847;27.84492" />' . "\n";
        echo '<meta name="ICBM" content="-26.68847, 27.84492" />' . "\n";
        
        // Enhanced keywords for local SEO
        $keywords = array(
            'Seventh-day Adventist church Vanderbijlpark',
            'SDA church Vaal triangle',
            'Agape SDA Vanderbijlpark',
            'SDA Vaal region churches',
            'Adventist church Gauteng',
            'Christian church Vanderbijlpark',
            'Sabbath worship Vaal',
            'Bible study Vanderbijlpark',
            'Church services Vaal Triangle',
            'Religious community Vanderbijlpark',
            'Adventist congregation South Africa',
            'Sabbath church Gauteng'
        );
        echo '<meta name="keywords" content="' . implode(', ', $keywords) . '" />' . "\n";
        
        // Additional local business meta
        echo '<meta name="business:contact_data:street_address" content="1 Gillchrist Street C.W.6" />' . "\n";
        echo '<meta name="business:contact_data:locality" content="Vanderbijlpark" />' . "\n";
        echo '<meta name="business:contact_data:region" content="Gauteng" />' . "\n";
        echo '<meta name="business:contact_data:postal_code" content="1900" />' . "\n";
        echo '<meta name="business:contact_data:country_name" content="South Africa" />' . "\n";
    }
}
add_action('wp_head', 'agape_local_seo_meta', 2);

/**
 * Enhanced Open Graph and Twitter Card Meta
 */
function agape_social_meta_tags() {
    if (is_front_page()) {
        // Enhanced Open Graph
        echo '<meta property="og:type" content="website" />' . "\n";
        echo '<meta property="og:locale" content="en_ZA" />' . "\n";
        echo '<meta property="og:site_name" content="Agape SDA Church Vanderbijlpark" />' . "\n";
        echo '<meta property="place:location:latitude" content="-26.68847" />' . "\n";
        echo '<meta property="place:location:longitude" content="27.84492" />' . "\n";
        
        // Twitter Cards
        echo '<meta name="twitter:card" content="summary_large_image" />' . "\n";
        echo '<meta name="twitter:site" content="@AgapeSDAvdb" />' . "\n";
        echo '<meta name="twitter:creator" content="@AgapeSDAvdb" />' . "\n";
    }
}
add_action('wp_head', 'agape_social_meta_tags', 3);

/**
 * Custom Title Tag Enhancement
 */
function agape_custom_title($title) {
    if (is_front_page()) {
        return 'Agape SDA Church Vanderbijlpark – Seventh-day Adventist Church in the Vaal Triangle | Sabbath Worship & Community';
    }
    return $title;
}
add_filter('pre_get_document_title', 'agape_custom_title');

/**
 * Enhanced Meta Description
 */
function agape_meta_description() {
    if (is_front_page() && !has_action('wp_head', 'wpseo_frontend')) {
        echo '<meta name="description" content="Join Agape SDA Church in Vanderbijlpark for Sabbath worship, Bible study, and community fellowship. Serving the Vaal Triangle with Seventh-day Adventist faith, hope, and love since 1980." />' . "\n";
    }
}
add_action('wp_head', 'agape_meta_description', 4);

/**
 * Add Church Contact Information Widget
 */
function agape_church_info_shortcode($atts) {
    $atts = shortcode_atts(array(
        'show_address' => 'true',
        'show_phone' => 'true',
        'show_email' => 'true',
        'show_hours' => 'true'
    ), $atts);
    
    ob_start();
    ?>
    <div class="church-info" itemscope itemtype="https://schema.org/Church">
        <h3>Visit Us</h3>
        
        <?php if ($atts['show_address'] === 'true'): ?>
        <div class="contact-item">
            <strong>Address:</strong><br>
            <span itemprop="address" itemscope itemtype="https://schema.org/PostalAddress">
                <span itemprop="streetAddress">1 Gillchrist Street C.W.6</span><br>
                <span itemprop="addressLocality">Vanderbijlpark</span>, 
                <span itemprop="addressRegion">Gauteng</span> 
                <span itemprop="postalCode">1900</span><br>
                <span itemprop="addressCountry">South Africa</span>
            </span>
        </div>
        <?php endif; ?>
        
        <?php if ($atts['show_phone'] === 'true'): ?>
        <div class="contact-item">
            <strong>Phone:</strong><br>
            <span itemprop="telephone">+27 68 518 0531</span>
        </div>
        <?php endif; ?>
        
        <?php if ($atts['show_email'] === 'true'): ?>
        <div class="contact-item">
            <strong>Email:</strong><br>
            <span itemprop="email"><EMAIL></span>
        </div>
        <?php endif; ?>
        
        <?php if ($atts['show_hours'] === 'true'): ?>
        <div class="service-times">
            <strong>Sabbath Service Times:</strong><br>
            <span itemprop="openingHours" content="Sa 09:00-12:00">
                Saturday: 9:00 AM - 12:00 PM
            </span>
        </div>
        <?php endif; ?>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('agape_church_info', 'agape_church_info_shortcode');

/**
 * Add Breadcrumb Schema
 */
function agape_breadcrumb_schema() {
    if (!is_front_page()) {
        $breadcrumbs = array();
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => 1,
            'name' => 'Home',
            'item' => home_url()
        );

        if (is_single() || is_page()) {
            $breadcrumbs[] = array(
                '@type' => 'ListItem',
                'position' => 2,
                'name' => get_the_title(),
                'item' => get_permalink()
            );
        }

        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $breadcrumbs
        );

        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>' . "\n";
    }
}
add_action('wp_head', 'agape_breadcrumb_schema', 5);

/**
 * Add FAQ Schema for Homepage
 */
function agape_faq_schema() {
    if (is_front_page()) {
        $faqs = array(
            array(
                '@type' => 'Question',
                'name' => 'What time are Sabbath services at Agape SDA Church Vanderbijlpark?',
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => 'Sabbath services are held every Saturday from 9:00 AM to 12:00 PM. Sabbath School begins at 9:00 AM, followed by the worship service at 10:15 AM.'
                )
            ),
            array(
                '@type' => 'Question',
                'name' => 'Where is Agape SDA Church located in Vanderbijlpark?',
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => 'Agape SDA Church is located at 1 Gillchrist Street C.W.6, Vanderbijlpark, Gauteng 1900, South Africa. We serve the entire Vaal Triangle region.'
                )
            ),
            array(
                '@type' => 'Question',
                'name' => 'What is the Seventh-day Adventist Church?',
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => 'The Seventh-day Adventist Church is a Protestant Christian denomination that observes Saturday as the Sabbath and emphasizes the Second Coming of Jesus Christ, health ministry, and community service.'
                )
            ),
            array(
                '@type' => 'Question',
                'name' => 'Do I need to be a member to attend services?',
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => 'No, everyone is welcome to attend our services. We invite visitors to join us for Sabbath worship, Bible study, and fellowship regardless of their background or beliefs.'
                )
            )
        );

        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => $faqs
        );

        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>' . "\n";
    }
}
add_action('wp_head', 'agape_faq_schema', 6);

/**
 * Add Image Alt Text Enhancement
 */
function agape_enhance_image_alt_text($attr, $attachment, $size) {
    if (empty($attr['alt'])) {
        $attr['alt'] = 'Agape SDA Church Vanderbijlpark - Seventh-day Adventist Church in the Vaal Triangle';
    }
    return $attr;
}
add_filter('wp_get_attachment_image_attributes', 'agape_enhance_image_alt_text', 10, 3);

/**
 * Add Canonical URL for Better SEO
 */
function agape_canonical_url() {
    if (!is_singular()) return;

    $canonical = get_permalink();
    if ($canonical) {
        echo '<link rel="canonical" href="' . esc_url($canonical) . '" />' . "\n";
    }
}
add_action('wp_head', 'agape_canonical_url', 1);

/**
 * Add Hreflang for Multi-language Support (if needed)
 */
function agape_hreflang_tags() {
    if (is_front_page()) {
        echo '<link rel="alternate" hreflang="en-za" href="' . home_url() . '" />' . "\n";
        echo '<link rel="alternate" hreflang="en" href="' . home_url() . '" />' . "\n";
    }
}
add_action('wp_head', 'agape_hreflang_tags', 2);

/**
 * Optimize WordPress Head for SEO
 */
function agape_optimize_wp_head() {
    // Remove unnecessary WordPress head elements
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10);
}
add_action('init', 'agape_optimize_wp_head');

/**
 * Add Church-specific Shortcodes
 */

// Service Times Shortcode
function agape_service_times_shortcode($atts) {
    $atts = shortcode_atts(array(
        'style' => 'default'
    ), $atts);

    ob_start();
    ?>
    <div class="service-times-widget" itemscope itemtype="https://schema.org/Church">
        <h3>Service Times</h3>
        <div class="service-schedule" itemprop="openingHours" content="Sa 09:00-12:00">
            <div class="service-item">
                <strong>Sabbath Worship Service</strong><br>
                Saturday: 9:00 AM - 12:00 PM
            </div>
            <div class="service-item">
                <strong>Bible Study</strong><br>
                Wednesday: 7:00 PM - 8:30 PM
            </div>
            <div class="service-item">
                <strong>Prayer Meeting</strong><br>
                Wednesday: 7:30 PM - 8:30 PM
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('agape_service_times', 'agape_service_times_shortcode');

// Quick Contact Shortcode
function agape_quick_contact_shortcode($atts) {
    $atts = shortcode_atts(array(
        'show_map' => 'false'
    ), $atts);

    ob_start();
    ?>
    <div class="quick-contact-widget" itemscope itemtype="https://schema.org/Church">
        <h3>Contact Us</h3>
        <div class="contact-details">
            <p><strong>Phone:</strong> <span itemprop="telephone">+27 68 518 0531</span></p>
            <p><strong>Email:</strong> <span itemprop="email"><EMAIL></span></p>
            <div itemprop="address" itemscope itemtype="https://schema.org/PostalAddress">
                <p><strong>Address:</strong><br>
                <span itemprop="streetAddress">1 Gillchrist Street C.W.6</span><br>
                <span itemprop="addressLocality">Vanderbijlpark</span>,
                <span itemprop="addressRegion">Gauteng</span>
                <span itemprop="postalCode">1900</span></p>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('agape_quick_contact', 'agape_quick_contact_shortcode');

/**
 * Traffic-Driving Features
 */

// Add Click-to-Call functionality
function agape_click_to_call_shortcode($atts) {
    $atts = shortcode_atts(array(
        'text' => 'Call Now',
        'number' => '+27685180531'
    ), $atts);

    return '<a href="tel:' . esc_attr($atts['number']) . '" class="click-to-call-btn">' . esc_html($atts['text']) . '</a>';
}
add_shortcode('click_to_call', 'agape_click_to_call_shortcode');

// Add WhatsApp Contact Button
function agape_whatsapp_shortcode($atts) {
    $atts = shortcode_atts(array(
        'number' => '27685180531',
        'message' => 'Hi! I\'d like to know more about Agape SDA Church Vanderbijlpark',
        'text' => 'WhatsApp Us'
    ), $atts);

    $whatsapp_url = 'https://wa.me/' . $atts['number'] . '?text=' . urlencode($atts['message']);

    return '<a href="' . esc_url($whatsapp_url) . '" target="_blank" class="whatsapp-btn" rel="noopener">' . esc_html($atts['text']) . '</a>';
}
add_shortcode('whatsapp_contact', 'agape_whatsapp_shortcode');

// Add Google Maps Directions Shortcode
function agape_directions_shortcode($atts) {
    $atts = shortcode_atts(array(
        'text' => 'Get Directions'
    ), $atts);

    $maps_url = 'https://www.google.com/maps/dir/?api=1&destination=1+Gillchrist+Street+C.W.6,+Vanderbijlpark,+Gauteng,+South+Africa';

    return '<a href="' . esc_url($maps_url) . '" target="_blank" class="directions-btn" rel="noopener">' . esc_html($atts['text']) . '</a>';
}
add_shortcode('get_directions', 'agape_directions_shortcode');

// Add Event Countdown Timer
function agape_countdown_shortcode($atts) {
    $atts = shortcode_atts(array(
        'event' => 'sabbath',
        'title' => 'Next Sabbath Service'
    ), $atts);

    $next_sabbath = strtotime('next saturday 09:00');
    $countdown_date = date('Y-m-d H:i:s', $next_sabbath);

    ob_start();
    ?>
    <div class="countdown-widget">
        <h3><?php echo esc_html($atts['title']); ?></h3>
        <div class="countdown-timer" data-date="<?php echo esc_attr($countdown_date); ?>">
            <div class="countdown-item">
                <span class="countdown-number" id="days">0</span>
                <span class="countdown-label">Days</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="hours">0</span>
                <span class="countdown-label">Hours</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="minutes">0</span>
                <span class="countdown-label">Minutes</span>
            </div>
        </div>
        <p class="countdown-message">Join us for Sabbath worship at Agape SDA Church Vanderbijlpark!</p>
    </div>

    <script>
    function updateCountdown() {
        const countdownDate = new Date("<?php echo $countdown_date; ?>").getTime();
        const now = new Date().getTime();
        const distance = countdownDate - now;

        if (distance > 0) {
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));

            document.getElementById("days").innerHTML = days;
            document.getElementById("hours").innerHTML = hours;
            document.getElementById("minutes").innerHTML = minutes;
        }
    }

    updateCountdown();
    setInterval(updateCountdown, 60000); // Update every minute
    </script>
    <?php
    return ob_get_clean();
}
add_shortcode('countdown_timer', 'agape_countdown_shortcode');

// Add Social Media Follow Buttons
function agape_social_follow_shortcode($atts) {
    $atts = shortcode_atts(array(
        'facebook' => 'https://facebook.com/agapesdavanderbijlpark',
        'instagram' => 'https://instagram.com/agapesdavanderbijlpark',
        'youtube' => 'https://youtube.com/agapesdavanderbijlpark'
    ), $atts);

    ob_start();
    ?>
    <div class="social-follow-widget">
        <h3>Follow Agape SDA Church</h3>
        <div class="social-buttons">
            <a href="<?php echo esc_url($atts['facebook']); ?>" target="_blank" class="social-btn facebook" rel="noopener">
                📘 Facebook
            </a>
            <a href="<?php echo esc_url($atts['instagram']); ?>" target="_blank" class="social-btn instagram" rel="noopener">
                📷 Instagram
            </a>
            <a href="<?php echo esc_url($atts['youtube']); ?>" target="_blank" class="social-btn youtube" rel="noopener">
                📺 YouTube
            </a>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('social_follow', 'agape_social_follow_shortcode');

// Add Live Chat Widget (placeholder for future integration)
function agape_live_chat_shortcode($atts) {
    ob_start();
    ?>
    <div class="live-chat-widget">
        <h3>💬 Have Questions?</h3>
        <p>Chat with our church team about services, beliefs, or visiting our SDA church in Vanderbijlpark.</p>
        <div class="chat-options">
            <a href="tel:+27685180531" class="chat-option">📞 Call Now</a>
            <a href="https://wa.me/27685180531?text=Hi! I'd like to know more about Agape SDA Church" target="_blank" class="chat-option" rel="noopener">💬 WhatsApp</a>
            <a href="mailto:<EMAIL>" class="chat-option">✉️ Email</a>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('live_chat', 'agape_live_chat_shortcode');

// Add Newsletter Signup (placeholder for future integration)
function agape_newsletter_shortcode($atts) {
    ob_start();
    ?>
    <div class="newsletter-widget">
        <h3>📧 Stay Connected</h3>
        <p>Get weekly updates about services, events, and community news from Agape SDA Church Vanderbijlpark.</p>
        <div class="newsletter-form">
            <p><strong>Contact us to join our mailing list:</strong></p>
            <p>📞 +27 68 518 0531 | ✉️ <EMAIL></p>
            <p><em>We respect your privacy and never share your information.</em></p>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('newsletter_signup', 'agape_newsletter_shortcode');

/**
 * Content Submission System for Agape SDA Church
 * Allows registered users to submit content for admin approval
 */

// Register custom post types for submissions
function agape_register_submission_post_types() {
    // Blog Post Submissions
    register_post_type('blog_submission', array(
        'labels' => array(
            'name' => 'Blog Submissions',
            'singular_name' => 'Blog Submission',
            'add_new' => 'Add New Submission',
            'add_new_item' => 'Add New Blog Submission',
            'edit_item' => 'Edit Blog Submission',
            'new_item' => 'New Blog Submission',
            'view_item' => 'View Blog Submission',
            'search_items' => 'Search Blog Submissions',
            'not_found' => 'No blog submissions found',
            'not_found_in_trash' => 'No blog submissions found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => 'edit.php',
        'capability_type' => 'post',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'menu_icon' => 'dashicons-edit-page'
    ));

    // File Submissions
    register_post_type('file_submission', array(
        'labels' => array(
            'name' => 'File Submissions',
            'singular_name' => 'File Submission',
            'add_new' => 'Add New Submission',
            'add_new_item' => 'Add New File Submission',
            'edit_item' => 'Edit File Submission',
            'new_item' => 'New File Submission',
            'view_item' => 'View File Submission',
            'search_items' => 'Search File Submissions',
            'not_found' => 'No file submissions found',
            'not_found_in_trash' => 'No file submissions found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => 'upload.php',
        'capability_type' => 'post',
        'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'menu_icon' => 'dashicons-media-document'
    ));

    // Video Resource Submissions
    register_post_type('video_submission', array(
        'labels' => array(
            'name' => 'Video Submissions',
            'singular_name' => 'Video Submission',
            'add_new' => 'Add New Submission',
            'add_new_item' => 'Add New Video Submission',
            'edit_item' => 'Edit Video Submission',
            'new_item' => 'New Video Submission',
            'view_item' => 'View Video Submission',
            'search_items' => 'Search Video Submissions',
            'not_found' => 'No video submissions found',
            'not_found_in_trash' => 'No video submissions found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => 'edit.php?post_type=video_submission',
        'capability_type' => 'post',
        'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'menu_icon' => 'dashicons-video-alt3'
    ));

    // Book Submissions
    register_post_type('book_submission', array(
        'labels' => array(
            'name' => 'Book Submissions',
            'singular_name' => 'Book Submission',
            'add_new' => 'Add New Submission',
            'add_new_item' => 'Add New Book Submission',
            'edit_item' => 'Edit Book Submission',
            'new_item' => 'New Book Submission',
            'view_item' => 'View Book Submission',
            'search_items' => 'Search Book Submissions',
            'not_found' => 'No book submissions found',
            'not_found_in_trash' => 'No book submissions found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => 'edit.php?post_type=book_submission',
        'capability_type' => 'post',
        'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'menu_icon' => 'dashicons-book'
    ));
}
add_action('init', 'agape_register_submission_post_types');

// Create custom user role for content contributors
function agape_create_contributor_role() {
    add_role('agape_contributor', 'Agape Contributor', array(
        'read' => true,
        'edit_posts' => false,
        'delete_posts' => false,
        'publish_posts' => false,
        'upload_files' => true,
        'edit_blog_submissions' => true,
        'edit_file_submissions' => true,
        'edit_video_submissions' => true,
        'edit_book_submissions' => true,
        'read_blog_submissions' => true,
        'read_file_submissions' => true,
        'read_video_submissions' => true,
        'read_book_submissions' => true
    ));
}
add_action('init', 'agape_create_contributor_role');

// Add custom capabilities to administrator
function agape_add_admin_capabilities() {
    $admin_role = get_role('administrator');
    if ($admin_role) {
        $admin_role->add_cap('edit_blog_submissions');
        $admin_role->add_cap('edit_others_blog_submissions');
        $admin_role->add_cap('publish_blog_submissions');
        $admin_role->add_cap('read_private_blog_submissions');
        $admin_role->add_cap('delete_blog_submissions');
        $admin_role->add_cap('delete_private_blog_submissions');
        $admin_role->add_cap('delete_published_blog_submissions');
        $admin_role->add_cap('delete_others_blog_submissions');
        $admin_role->add_cap('edit_private_blog_submissions');
        $admin_role->add_cap('edit_published_blog_submissions');

        // Repeat for other submission types
        $submission_types = array('file_submissions', 'video_submissions', 'book_submissions');
        foreach ($submission_types as $type) {
            $admin_role->add_cap('edit_' . $type);
            $admin_role->add_cap('edit_others_' . $type);
            $admin_role->add_cap('publish_' . $type);
            $admin_role->add_cap('read_private_' . $type);
            $admin_role->add_cap('delete_' . $type);
            $admin_role->add_cap('delete_private_' . $type);
            $admin_role->add_cap('delete_published_' . $type);
            $admin_role->add_cap('delete_others_' . $type);
            $admin_role->add_cap('edit_private_' . $type);
            $admin_role->add_cap('edit_published_' . $type);
        }
    }
}
add_action('init', 'agape_add_admin_capabilities');

// Security: Restrict submission access to logged-in users only
function agape_restrict_submission_access() {
    if (!is_user_logged_in() && (is_page('submit-content') || is_page('contributor-dashboard'))) {
        wp_redirect(wp_login_url(get_permalink()));
        exit;
    }
}
add_action('template_redirect', 'agape_restrict_submission_access');

// Handle content submissions via AJAX
function agape_handle_content_submission() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['agape_submission_nonce'], 'agape_submit_content')) {
        wp_die('Security check failed');
    }

    // Check if user is logged in and has permission
    if (!is_user_logged_in() || !current_user_can('agape_contributor')) {
        wp_die('You do not have permission to submit content');
    }

    $submission_type = sanitize_text_field($_POST['submission_type']);
    $title = sanitize_text_field($_POST['title']);
    $content = wp_kses_post($_POST['content']);
    $category = sanitize_text_field($_POST['category']);

    // Create submission post
    $post_data = array(
        'post_title' => $title,
        'post_content' => $content,
        'post_status' => 'draft', // Always draft until admin approval
        'post_author' => get_current_user_id(),
        'post_type' => $submission_type . '_submission'
    );

    $post_id = wp_insert_post($post_data);

    if ($post_id) {
        // Add custom fields based on submission type
        update_post_meta($post_id, '_submission_category', $category);
        update_post_meta($post_id, '_submission_status', 'pending');
        update_post_meta($post_id, '_submitted_by', get_current_user_id());
        update_post_meta($post_id, '_submission_date', current_time('mysql'));

        // Handle file uploads
        if (!empty($_FILES['submission_file'])) {
            $uploaded_file = wp_handle_upload($_FILES['submission_file'], array('test_form' => false));
            if ($uploaded_file && !isset($uploaded_file['error'])) {
                update_post_meta($post_id, '_submission_file_url', $uploaded_file['url']);
                update_post_meta($post_id, '_submission_file_path', $uploaded_file['file']);
            }
        }

        // Handle additional fields based on type
        switch ($submission_type) {
            case 'blog':
                update_post_meta($post_id, '_blog_excerpt', sanitize_textarea_field($_POST['excerpt']));
                update_post_meta($post_id, '_blog_tags', sanitize_text_field($_POST['tags']));
                break;

            case 'file':
                update_post_meta($post_id, '_file_type', sanitize_text_field($_POST['file_type']));
                update_post_meta($post_id, '_file_size', sanitize_text_field($_POST['file_size']));
                update_post_meta($post_id, '_file_duration', sanitize_text_field($_POST['file_duration']));
                break;

            case 'video':
                update_post_meta($post_id, '_video_url', esc_url_raw($_POST['video_url']));
                update_post_meta($post_id, '_video_duration', sanitize_text_field($_POST['video_duration']));
                update_post_meta($post_id, '_video_speaker', sanitize_text_field($_POST['speaker']));
                update_post_meta($post_id, '_related_links', sanitize_textarea_field($_POST['related_links']));
                break;

            case 'book':
                update_post_meta($post_id, '_book_author', sanitize_text_field($_POST['author']));
                update_post_meta($post_id, '_book_pages', sanitize_text_field($_POST['pages']));
                update_post_meta($post_id, '_book_isbn', sanitize_text_field($_POST['isbn']));
                break;
        }

        // Send notification to admin
        agape_notify_admin_new_submission($post_id, $submission_type);

        wp_send_json_success(array(
            'message' => 'Content submitted successfully! It will be reviewed before publication.',
            'post_id' => $post_id
        ));
    } else {
        wp_send_json_error('Failed to submit content. Please try again.');
    }
}
add_action('wp_ajax_agape_submit_content', 'agape_handle_content_submission');

// Send email notification to admin when new content is submitted
function agape_notify_admin_new_submission($post_id, $type) {
    $admin_email = get_option('admin_email');
    $post = get_post($post_id);
    $author = get_userdata($post->post_author);

    $subject = 'New ' . ucfirst($type) . ' Submission - Agape SDA Church';
    $message = "A new {$type} submission has been received:\n\n";
    $message .= "Title: {$post->post_title}\n";
    $message .= "Submitted by: {$author->display_name} ({$author->user_email})\n";
    $message .= "Date: " . date('Y-m-d H:i:s') . "\n\n";
    $message .= "Please review and approve in the WordPress admin dashboard:\n";
    $message .= admin_url("edit.php?post_type={$type}_submission") . "\n\n";
    $message .= "Content preview:\n" . wp_trim_words($post->post_content, 50);

    wp_mail($admin_email, $subject, $message);
}

// Add admin notice for pending submissions
function agape_admin_submission_notices() {
    $submission_types = array('blog_submission', 'file_submission', 'video_submission', 'book_submission');
    $total_pending = 0;

    foreach ($submission_types as $type) {
        $pending = wp_count_posts($type);
        if (isset($pending->draft)) {
            $total_pending += $pending->draft;
        }
    }

    if ($total_pending > 0) {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>Agape SDA Church:</strong> You have ' . $total_pending . ' pending content submission(s) awaiting review.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'agape_admin_submission_notices');

// Add quick approval/rejection actions
function agape_add_submission_actions($actions, $post) {
    if (in_array($post->post_type, array('blog_submission', 'file_submission', 'video_submission', 'book_submission'))) {
        if ($post->post_status === 'draft') {
            $actions['approve'] = '<a href="' . wp_nonce_url(admin_url('admin-post.php?action=approve_submission&post_id=' . $post->ID), 'approve_submission_' . $post->ID) . '">Approve & Publish</a>';
            $actions['reject'] = '<a href="' . wp_nonce_url(admin_url('admin-post.php?action=reject_submission&post_id=' . $post->ID), 'reject_submission_' . $post->ID) . '" style="color: #d63638;">Reject</a>';
        }
    }
    return $actions;
}
add_filter('post_row_actions', 'agape_add_submission_actions', 10, 2);

// Handle submission approval
function agape_handle_submission_approval() {
    if (!current_user_can('administrator')) {
        wp_die('You do not have permission to approve submissions.');
    }

    $post_id = intval($_GET['post_id']);

    if (!wp_verify_nonce($_GET['_wpnonce'], 'approve_submission_' . $post_id)) {
        wp_die('Security check failed.');
    }

    $submission = get_post($post_id);
    if (!$submission) {
        wp_die('Submission not found.');
    }

    // Create actual content post based on submission type
    $submission_type = str_replace('_submission', '', $submission->post_type);

    switch ($submission_type) {
        case 'blog':
            $new_post_type = 'post';
            break;
        case 'file':
            $new_post_type = 'attachment';
            break;
        case 'video':
            $new_post_type = 'agape_video'; // Custom post type for videos
            break;
        case 'book':
            $new_post_type = 'agape_book'; // Custom post type for books
            break;
        default:
            $new_post_type = 'post';
    }

    // Create the actual content post
    $new_post_data = array(
        'post_title' => $submission->post_title,
        'post_content' => $submission->post_content,
        'post_status' => 'publish',
        'post_author' => $submission->post_author,
        'post_type' => $new_post_type,
        'post_date' => current_time('mysql')
    );

    $new_post_id = wp_insert_post($new_post_data);

    if ($new_post_id) {
        // Copy all meta data from submission to new post
        $meta_data = get_post_meta($post_id);
        foreach ($meta_data as $key => $values) {
            foreach ($values as $value) {
                update_post_meta($new_post_id, $key, $value);
            }
        }

        // Copy featured image if exists
        $thumbnail_id = get_post_thumbnail_id($post_id);
        if ($thumbnail_id) {
            set_post_thumbnail($new_post_id, $thumbnail_id);
        }

        // Mark submission as approved
        update_post_meta($post_id, '_submission_status', 'approved');
        update_post_meta($post_id, '_approved_post_id', $new_post_id);
        update_post_meta($post_id, '_approved_by', get_current_user_id());
        update_post_meta($post_id, '_approved_date', current_time('mysql'));

        // Change submission status to published
        wp_update_post(array(
            'ID' => $post_id,
            'post_status' => 'publish'
        ));

        // Notify submitter
        agape_notify_submitter_approval($post_id, $new_post_id);

        wp_redirect(admin_url('edit.php?post_type=' . $submission->post_type . '&approved=1'));
    } else {
        wp_redirect(admin_url('edit.php?post_type=' . $submission->post_type . '&error=1'));
    }
    exit;
}
add_action('admin_post_approve_submission', 'agape_handle_submission_approval');

// Handle submission rejection
function agape_handle_submission_rejection() {
    if (!current_user_can('administrator')) {
        wp_die('You do not have permission to reject submissions.');
    }

    $post_id = intval($_GET['post_id']);

    if (!wp_verify_nonce($_GET['_wpnonce'], 'reject_submission_' . $post_id)) {
        wp_die('Security check failed.');
    }

    $submission = get_post($post_id);
    if (!$submission) {
        wp_die('Submission not found.');
    }

    // Mark submission as rejected
    update_post_meta($post_id, '_submission_status', 'rejected');
    update_post_meta($post_id, '_rejected_by', get_current_user_id());
    update_post_meta($post_id, '_rejected_date', current_time('mysql'));

    // Move to trash
    wp_trash_post($post_id);

    // Notify submitter
    agape_notify_submitter_rejection($post_id);

    wp_redirect(admin_url('edit.php?post_type=' . $submission->post_type . '&rejected=1'));
    exit;
}
add_action('admin_post_reject_submission', 'agape_handle_submission_rejection');

// Notify submitter of approval
function agape_notify_submitter_approval($submission_id, $published_id) {
    $submission = get_post($submission_id);
    $author = get_userdata($submission->post_author);

    $subject = 'Your submission has been approved - Agape SDA Church';
    $message = "Dear {$author->display_name},\n\n";
    $message .= "Your submission '{$submission->post_title}' has been approved and published on the Agape SDA Church website.\n\n";
    $message .= "You can view it at: " . get_permalink($published_id) . "\n\n";
    $message .= "Thank you for contributing to our church community!\n\n";
    $message .= "Blessings,\nAgape SDA Church Team";

    wp_mail($author->user_email, $subject, $message);
}

// Notify submitter of rejection
function agape_notify_submitter_rejection($submission_id) {
    $submission = get_post($submission_id);
    $author = get_userdata($submission->post_author);

    $subject = 'Submission Update - Agape SDA Church';
    $message = "Dear {$author->display_name},\n\n";
    $message .= "Thank you for your submission '{$submission->post_title}'. ";
    $message .= "After review, we are unable to publish this content at this time.\n\n";
    $message .= "Please feel free to submit revised content or contact us for feedback.\n\n";
    $message .= "Blessings,\nAgape SDA Church Team";

    wp_mail($author->user_email, $subject, $message);
}

// Register navigation menus
function agape_register_menus() {
    register_nav_menus(array(
        'primary' => 'Primary Menu',
        'footer' => 'Footer Menu',
        'contributor' => 'Contributor Menu'
    ));
}
add_action('init', 'agape_register_menus');

// Add custom menu items for new pages
function agape_add_custom_menu_items() {
    // Only run this once to avoid duplicates
    if (get_option('agape_menu_items_added')) {
        return;
    }

    // Get the primary menu
    $menu_name = 'primary';
    $locations = get_nav_menu_locations();

    if (isset($locations[$menu_name])) {
        $menu_id = $locations[$menu_name];

        // Add new menu items
        $menu_items = array(
            array(
                'menu-item-title' => 'Books & Literature',
                'menu-item-url' => home_url('/books'),
                'menu-item-status' => 'publish',
                'menu-item-type' => 'custom',
                'menu-item-object' => 'custom'
            ),
            array(
                'menu-item-title' => 'Blog',
                'menu-item-url' => home_url('/blog'),
                'menu-item-status' => 'publish',
                'menu-item-type' => 'custom',
                'menu-item-object' => 'custom'
            ),
            array(
                'menu-item-title' => 'File Resources',
                'menu-item-url' => home_url('/files'),
                'menu-item-status' => 'publish',
                'menu-item-type' => 'custom',
                'menu-item-object' => 'custom'
            ),
            array(
                'menu-item-title' => 'Video Resources',
                'menu-item-url' => home_url('/resources'),
                'menu-item-status' => 'publish',
                'menu-item-type' => 'custom',
                'menu-item-object' => 'custom'
            ),
            array(
                'menu-item-title' => 'Bible Study',
                'menu-item-url' => home_url('/bible-study'),
                'menu-item-status' => 'publish',
                'menu-item-type' => 'custom',
                'menu-item-object' => 'custom'
            )
        );

        foreach ($menu_items as $item) {
            wp_update_nav_menu_item($menu_id, 0, $item);
        }

        // Add contributor menu items (only visible to logged-in users)
        if (is_user_logged_in()) {
            $contributor_items = array(
                array(
                    'menu-item-title' => 'Contributor Login',
                    'menu-item-url' => home_url('/contributor-login'),
                    'menu-item-status' => 'publish',
                    'menu-item-type' => 'custom',
                    'menu-item-object' => 'custom'
                ),
                array(
                    'menu-item-title' => 'Dashboard',
                    'menu-item-url' => home_url('/contributor-dashboard'),
                    'menu-item-status' => 'publish',
                    'menu-item-type' => 'custom',
                    'menu-item-object' => 'custom'
                )
            );

            foreach ($contributor_items as $item) {
                wp_update_nav_menu_item($menu_id, 0, $item);
            }
        }

        // Mark as completed
        update_option('agape_menu_items_added', true);
    }
}
add_action('wp_loaded', 'agape_add_custom_menu_items');

// Add conditional menu items based on user login status
function agape_modify_nav_menu_items($items, $args) {
    if ($args->theme_location == 'primary') {
        $current_user = wp_get_current_user();

        if (is_user_logged_in()) {
            // Add dashboard link for logged-in contributors
            if (in_array('agape_contributor', $current_user->roles) || in_array('administrator', $current_user->roles)) {
                $dashboard_item = '<li class="menu-item contributor-dashboard"><a href="' . home_url('/contributor-dashboard') . '">📝 Dashboard</a></li>';
                $logout_item = '<li class="menu-item contributor-logout"><a href="' . wp_logout_url(home_url()) . '">🚪 Logout</a></li>';
                $items .= $dashboard_item . $logout_item;
            }
        } else {
            // Add login link for non-logged-in users
            $login_item = '<li class="menu-item contributor-login"><a href="' . home_url('/contributor-login') . '">🔐 Contributor Login</a></li>';
            $items .= $login_item;
        }
    }

    return $items;
}
add_filter('wp_nav_menu_items', 'agape_modify_nav_menu_items', 10, 2);

// Add admin menu for managing submissions
function agape_add_admin_menu() {
    add_menu_page(
        'Content Submissions',
        'Submissions',
        'manage_options',
        'agape-submissions',
        'agape_submissions_admin_page',
        'dashicons-upload',
        30
    );

    add_submenu_page(
        'agape-submissions',
        'Pending Submissions',
        'Pending',
        'manage_options',
        'agape-pending-submissions',
        'agape_pending_submissions_page'
    );

    add_submenu_page(
        'agape-submissions',
        'Approved Submissions',
        'Approved',
        'manage_options',
        'agape-approved-submissions',
        'agape_approved_submissions_page'
    );

    add_submenu_page(
        'agape-submissions',
        'Manage Contributors',
        'Contributors',
        'manage_options',
        'agape-contributors',
        'agape_contributors_page'
    );
}
add_action('admin_menu', 'agape_add_admin_menu');

// Admin page for managing all submissions
function agape_submissions_admin_page() {
    ?>
    <div class="wrap">
        <h1>📋 Content Submissions Dashboard</h1>

        <div class="agape-admin-dashboard">
            <div class="dashboard-stats">
                <?php
                $submission_types = array('blog_submission', 'file_submission', 'video_submission', 'book_submission');
                $total_pending = 0;
                $total_approved = 0;

                foreach ($submission_types as $type) {
                    $pending = wp_count_posts($type);
                    if (isset($pending->draft)) {
                        $total_pending += $pending->draft;
                    }
                    if (isset($pending->publish)) {
                        $total_approved += $pending->publish;
                    }
                }
                ?>

                <div class="stat-box">
                    <h3>⏳ Pending Review</h3>
                    <div class="stat-number"><?php echo $total_pending; ?></div>
                    <a href="<?php echo admin_url('admin.php?page=agape-pending-submissions'); ?>" class="stat-link">Review Now</a>
                </div>

                <div class="stat-box">
                    <h3>✅ Approved</h3>
                    <div class="stat-number"><?php echo $total_approved; ?></div>
                    <a href="<?php echo admin_url('admin.php?page=agape-approved-submissions'); ?>" class="stat-link">View All</a>
                </div>

                <div class="stat-box">
                    <h3>👥 Contributors</h3>
                    <div class="stat-number"><?php echo count(get_users(array('role' => 'agape_contributor'))); ?></div>
                    <a href="<?php echo admin_url('admin.php?page=agape-contributors'); ?>" class="stat-link">Manage</a>
                </div>
            </div>

            <div class="quick-actions">
                <h2>🚀 Quick Actions</h2>
                <div class="action-buttons">
                    <a href="<?php echo admin_url('admin.php?page=agape-pending-submissions'); ?>" class="button button-primary">Review Pending Submissions</a>
                    <a href="<?php echo admin_url('users.php?role=agape_contributor'); ?>" class="button">Manage Contributors</a>
                    <a href="<?php echo home_url('/contributor-dashboard'); ?>" class="button">View Contributor Dashboard</a>
                </div>
            </div>

            <div class="recent-activity">
                <h2>📈 Recent Activity</h2>
                <?php
                $recent_submissions = get_posts(array(
                    'post_type' => $submission_types,
                    'post_status' => array('draft', 'publish'),
                    'numberposts' => 10,
                    'orderby' => 'date',
                    'order' => 'DESC'
                ));

                if ($recent_submissions) {
                    echo '<table class="wp-list-table widefat fixed striped">';
                    echo '<thead><tr><th>Title</th><th>Type</th><th>Author</th><th>Status</th><th>Date</th><th>Actions</th></tr></thead>';
                    echo '<tbody>';

                    foreach ($recent_submissions as $submission) {
                        $author = get_userdata($submission->post_author);
                        $type = str_replace('_submission', '', $submission->post_type);
                        $status = $submission->post_status === 'draft' ? 'Pending' : 'Approved';
                        $status_class = $submission->post_status === 'draft' ? 'pending' : 'approved';

                        echo '<tr>';
                        echo '<td><strong>' . esc_html($submission->post_title) . '</strong></td>';
                        echo '<td>' . ucfirst($type) . '</td>';
                        echo '<td>' . esc_html($author->display_name) . '</td>';
                        echo '<td><span class="status-' . $status_class . '">' . $status . '</span></td>';
                        echo '<td>' . date('M j, Y', strtotime($submission->post_date)) . '</td>';
                        echo '<td>';

                        if ($submission->post_status === 'draft') {
                            echo '<a href="' . wp_nonce_url(admin_url('admin-post.php?action=approve_submission&post_id=' . $submission->ID), 'approve_submission_' . $submission->ID) . '" class="button button-small">Approve</a> ';
                            echo '<a href="' . wp_nonce_url(admin_url('admin-post.php?action=reject_submission&post_id=' . $submission->ID), 'reject_submission_' . $submission->ID) . '" class="button button-small">Reject</a>';
                        } else {
                            echo '<span class="approved-text">✅ Published</span>';
                        }

                        echo '</td>';
                        echo '</tr>';
                    }

                    echo '</tbody></table>';
                } else {
                    echo '<p>No recent submissions.</p>';
                }
                ?>
            </div>
        </div>
    </div>

    <style>
    .agape-admin-dashboard .dashboard-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-box {
        background: white;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #ccd0d4;
        text-align: center;
        flex: 1;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #1976d2;
        margin: 10px 0;
    }

    .stat-link {
        color: #1976d2;
        text-decoration: none;
        font-weight: 600;
    }

    .quick-actions,
    .recent-activity {
        background: white;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #ccd0d4;
        margin-bottom: 20px;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .status-pending {
        color: #856404;
        background: #fff3cd;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 0.85rem;
    }

    .status-approved {
        color: #155724;
        background: #d4edda;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 0.85rem;
    }

    .approved-text {
        color: #155724;
        font-weight: 600;
    }
    </style>
    <?php
}

// Pending submissions page
function agape_pending_submissions_page() {
    ?>
    <div class="wrap">
        <h1>⏳ Pending Submissions</h1>
        <p>Review and approve content submissions from contributors.</p>

        <?php
        $submission_types = array('blog_submission', 'file_submission', 'video_submission', 'book_submission');
        $pending_submissions = get_posts(array(
            'post_type' => $submission_types,
            'post_status' => 'draft',
            'numberposts' => -1,
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        if ($pending_submissions) {
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead><tr><th>Title</th><th>Type</th><th>Author</th><th>Submitted</th><th>Actions</th></tr></thead>';
            echo '<tbody>';

            foreach ($pending_submissions as $submission) {
                $author = get_userdata($submission->post_author);
                $type = str_replace('_submission', '', $submission->post_type);

                echo '<tr>';
                echo '<td><strong>' . esc_html($submission->post_title) . '</strong><br>';
                echo '<small>' . wp_trim_words($submission->post_content, 20) . '</small></td>';
                echo '<td>' . ucfirst($type) . '</td>';
                echo '<td>' . esc_html($author->display_name) . '<br><small>' . esc_html($author->user_email) . '</small></td>';
                echo '<td>' . date('M j, Y g:i A', strtotime($submission->post_date)) . '</td>';
                echo '<td>';
                echo '<a href="' . wp_nonce_url(admin_url('admin-post.php?action=approve_submission&post_id=' . $submission->ID), 'approve_submission_' . $submission->ID) . '" class="button button-primary">✅ Approve</a> ';
                echo '<a href="' . wp_nonce_url(admin_url('admin-post.php?action=reject_submission&post_id=' . $submission->ID), 'reject_submission_' . $submission->ID) . '" class="button">❌ Reject</a> ';
                echo '<a href="' . get_edit_post_link($submission->ID) . '" class="button">✏️ Edit</a>';
                echo '</td>';
                echo '</tr>';
            }

            echo '</tbody></table>';
        } else {
            echo '<div class="notice notice-info"><p>No pending submissions at this time.</p></div>';
        }
        ?>
    </div>
    <?php
}

// Approved submissions page
function agape_approved_submissions_page() {
    ?>
    <div class="wrap">
        <h1>✅ Approved Submissions</h1>
        <p>View all approved and published content submissions.</p>

        <?php
        $submission_types = array('blog_submission', 'file_submission', 'video_submission', 'book_submission');
        $approved_submissions = get_posts(array(
            'post_type' => $submission_types,
            'post_status' => 'publish',
            'numberposts' => 50,
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        if ($approved_submissions) {
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead><tr><th>Title</th><th>Type</th><th>Author</th><th>Approved</th><th>Published Content</th></tr></thead>';
            echo '<tbody>';

            foreach ($approved_submissions as $submission) {
                $author = get_userdata($submission->post_author);
                $type = str_replace('_submission', '', $submission->post_type);
                $published_id = get_post_meta($submission->ID, '_approved_post_id', true);

                echo '<tr>';
                echo '<td><strong>' . esc_html($submission->post_title) . '</strong></td>';
                echo '<td>' . ucfirst($type) . '</td>';
                echo '<td>' . esc_html($author->display_name) . '</td>';
                echo '<td>' . date('M j, Y', strtotime($submission->post_date)) . '</td>';
                echo '<td>';

                if ($published_id) {
                    echo '<a href="' . get_permalink($published_id) . '" class="button" target="_blank">👁️ View Published</a>';
                } else {
                    echo '<span class="approved-text">✅ Approved</span>';
                }

                echo '</td>';
                echo '</tr>';
            }

            echo '</tbody></table>';
        } else {
            echo '<div class="notice notice-info"><p>No approved submissions yet.</p></div>';
        }
        ?>
    </div>
    <?php
}

// Contributors management page
function agape_contributors_page() {
    ?>
    <div class="wrap">
        <h1>👥 Manage Contributors</h1>
        <p>Manage users who can submit content to the website.</p>

        <div class="contributor-actions">
            <a href="<?php echo admin_url('user-new.php'); ?>" class="button button-primary">➕ Add New Contributor</a>
            <a href="<?php echo home_url('/contributor-login'); ?>" class="button" target="_blank">🔗 Contributor Login Page</a>
        </div>

        <?php
        $contributors = get_users(array('role' => 'agape_contributor'));

        if ($contributors) {
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead><tr><th>Name</th><th>Email</th><th>Registered</th><th>Submissions</th><th>Actions</th></tr></thead>';
            echo '<tbody>';

            foreach ($contributors as $contributor) {
                $submission_count = count(get_posts(array(
                    'post_type' => array('blog_submission', 'file_submission', 'video_submission', 'book_submission'),
                    'author' => $contributor->ID,
                    'post_status' => array('draft', 'publish'),
                    'numberposts' => -1
                )));

                echo '<tr>';
                echo '<td><strong>' . esc_html($contributor->display_name) . '</strong></td>';
                echo '<td>' . esc_html($contributor->user_email) . '</td>';
                echo '<td>' . date('M j, Y', strtotime($contributor->user_registered)) . '</td>';
                echo '<td>' . $submission_count . ' submissions</td>';
                echo '<td>';
                echo '<a href="' . get_edit_user_link($contributor->ID) . '" class="button">✏️ Edit</a> ';
                echo '<a href="' . admin_url('edit.php?post_type=blog_submission&author=' . $contributor->ID) . '" class="button">📋 View Submissions</a>';
                echo '</td>';
                echo '</tr>';
            }

            echo '</tbody></table>';
        } else {
            echo '<div class="notice notice-info"><p>No contributors found. <a href="' . admin_url('user-new.php') . '">Add the first contributor</a>.</p></div>';
        }
        ?>

        <div class="contributor-info">
            <h2>ℹ️ About Contributors</h2>
            <p><strong>Contributor Role:</strong> Users with the "Agape Contributor" role can submit content for review but cannot publish directly.</p>
            <p><strong>Permissions:</strong> Contributors can upload files, submit blog posts, videos, and book resources, but all content requires admin approval.</p>
            <p><strong>Security:</strong> All submissions are saved as drafts and require manual approval before publication.</p>
        </div>

        <style>
        .contributor-actions {
            margin: 20px 0;
        }

        .contributor-actions .button {
            margin-right: 10px;
        }

        .contributor-info {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }

        .contributor-info h2 {
            margin-top: 0;
            color: #1976d2;
        }
        </style>
    </div>
    <?php
}
